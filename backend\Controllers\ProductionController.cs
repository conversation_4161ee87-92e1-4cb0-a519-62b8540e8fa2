using Excel.Hubs;
using Excel.Models;
using Excel.Services.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.SignalR;
using OfficeOpenXml;

namespace Excel.Controllers;

[ApiController]
[Route("api/[controller]")]
public class ProductionController : ControllerBase
{
    private readonly IProductionService _productionService;
    private readonly IHubContext<NotificationHub> _hubContext;
    private readonly IUserService _userService;
    private readonly IEmailService _emailService;
    private readonly IEditHistoryService _editHistoryService;

    public ProductionController(
        IProductionService productionService,
        IHubContext<NotificationHub> hubContext,
        IUserService userService,
        IEmailService emailService,
        IEditHistoryService editHistoryService
    )
    {
        _productionService = productionService;
        _hubContext = hubContext;
        _userService = userService;
        _emailService = emailService;
        _editHistoryService = editHistoryService;
    }

    // GET: api/Production
    [HttpGet]
    public async Task<ActionResult<IEnumerable<Production>>> GetProductions()
    {
        try
        {
            var productions = await _productionService.GetActiveProductionsAsync();
            return Ok(productions);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Sunucu hatası: {ex.Message}");
        }
    }

    // GET: api/Production/5
    [HttpGet("{id}")]
    public async Task<ActionResult<Production>> GetProduction(int id)
    {
        try
        {
            var production = await _productionService.GetProductionByIdAsync(id);
            if (production == null)
            {
                return NotFound("Üretim bulunamadı");
            }

            return Ok(production);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Sunucu hatası: {ex.Message}");
        }
    }

    // POST: api/Production/create-simple
    [HttpPost("create-simple")]
    public async Task<ActionResult<Production>> CreateSimpleProduction(CreateProductionDto dto)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }

        try
        {
            var production = await _productionService.CreateProductionAsync(dto);

            await _hubContext.Clients.All.SendAsync("ProductionCreated", production);

            return CreatedAtAction(nameof(GetProduction), new { id = production.Id }, production);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Sunucu hatası: {ex.Message}");
        }
    }

    // POST: api/Production
    [HttpPost]
    public async Task<ActionResult<Production>> CreateProduction(Production production)
    {
        try
        {
            var dto = new CreateProductionDto
            {
                Name = production.Name,
                Surname = production.Surname,
                Notes = production.Notes,
                PStar = production.PStar,
                Greening = production.Greening,
                AdminApproval = production.AdminApproval,
                StlPrint = production.StlPrint,
                Technician = production.Technician,
                ApprovalDate = production.ApprovalDate,
                TargetDate = production.TargetDate,
                Replication = production.Replication,
                Model = production.Model,
                PlatePressing = production.PlatePressing,
                FineCut = production.FineCut,
                Packaging = production.Packaging,
                Shipping = production.Shipping,
                ShippingType = production.ShippingType,
                AdminNote = production.AdminNote,
            };

            var createdProduction = await _productionService.CreateProductionAsync(dto);
            return CreatedAtAction(
                nameof(GetProduction),
                new { id = createdProduction.Id },
                createdProduction
            );
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Sunucu hatası: {ex.Message}");
        }
    }

    // PUT: api/Production/5
    [HttpPut("{id}")]
    public async Task<IActionResult> UpdateProduction(int id, Production production)
    {
        if (id != production.Id)
        {
            return BadRequest("ID uyuşmazlığı");
        }

        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }

        try
        {
            var dto = new CreateProductionDto
            {
                Name = production.Name,
                Surname = production.Surname,
                Notes = production.Notes,
                PStar = production.PStar,
                Greening = production.Greening,
                AdminApproval = production.AdminApproval,
                StlPrint = production.StlPrint,
                Technician = production.Technician,
                ApprovalDate = production.ApprovalDate,
                TargetDate = production.TargetDate,
                Replication = production.Replication,
                Model = production.Model,
                PlatePressing = production.PlatePressing,
                FineCut = production.FineCut,
                Packaging = production.Packaging,
                Shipping = production.Shipping,
                ShippingType = production.ShippingType,
                IsArchived = production.IsArchived,
                AdminNote = production.AdminNote,
            };

            var userId = User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value;
            var updatedProduction = await _productionService.UpdateProductionAsync(id, dto, userId);

            await _hubContext.Clients.All.SendAsync("ProductionUpdated", updatedProduction);

            return Ok(updatedProduction);
        }
        catch (InvalidOperationException ex)
        {
            return NotFound(ex.Message);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Sunucu hatası: {ex.Message}");
        }
    }

    // DELETE: api/Production/5
    [HttpDelete("{id}")]
    public async Task<IActionResult> DeleteProduction(int id)
    {
        try
        {
            await _productionService.DeleteProductionAsync(id);

            await _hubContext.Clients.All.SendAsync("ProductionDeleted", id);

            return NoContent();
        }
        catch (InvalidOperationException ex)
        {
            return NotFound(ex.Message);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Sunucu hatası: {ex.Message}");
        }
    }

    // GET: api/Production/search?term=searchTerm
    [HttpGet("search")]
    public async Task<ActionResult<IEnumerable<Production>>> SearchProductions(string term)
    {
        try
        {
            if (string.IsNullOrEmpty(term))
            {
                return await GetProductions();
            }

            // Note: Search functionality would need to be implemented in the service layer
            // For now, we'll return all active productions
            var productions = await _productionService.GetActiveProductionsAsync();
            var filteredProductions = productions.Where(p =>
                p.Name.Contains(term, StringComparison.OrdinalIgnoreCase)
                || p.Surname.Contains(term, StringComparison.OrdinalIgnoreCase)
                || (p.Notes != null && p.Notes.Contains(term, StringComparison.OrdinalIgnoreCase))
            );

            return Ok(filteredProductions);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Sunucu hatası: {ex.Message}");
        }
    }

    // GET: api/Production/stats
    [HttpGet("stats")]
    public async Task<ActionResult<object>> GetProductionStats()
    {
        try
        {
            var activeProductions = await _productionService.GetActiveProductionsAsync();
            var total = activeProductions.Count();
            var pStar = activeProductions.Count(p => p.PStar);
            var greening = activeProductions.Count(p => p.Greening);
            var approved = activeProductions.Count(p => p.AdminApproval);
            var stlPrint = activeProductions.Count(p => p.StlPrint);
            var inProgress = activeProductions.Count(p =>
                p.Model || p.PlatePressing || p.FineCut || p.Packaging
            );
            var shipped = activeProductions.Count(p => p.Shipping);

            return new
            {
                Total = total,
                PStar = pStar,
                Greening = greening,
                Approved = approved,
                StlPrint = stlPrint,
                InProgress = inProgress,
                Shipped = shipped,
            };
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Sunucu hatası: {ex.Message}");
        }
    }

    // GET: api/Production/technician/{technicianId}
    [HttpGet("technician/{technicianId}")]
    public async Task<ActionResult<IEnumerable<Production>>> GetProductionsByTechnician(
        int technicianId
    )
    {
        try
        {
            var productions = await _productionService.GetActiveProductionsAsync();
            var technicianProductions = productions.Where(p => p.Technician == technicianId);
            return Ok(technicianProductions);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Sunucu hatası: {ex.Message}");
        }
    }

    // GET: api/Production/overdue
    [HttpGet("overdue")]
    public async Task<ActionResult<IEnumerable<Production>>> GetOverdueProductions()
    {
        try
        {
            var today = DateOnly.FromDateTime(DateTime.Today);
            var productions = await _productionService.GetActiveProductionsAsync();
            var overdueProductions = productions.Where(p =>
                p.TargetDate.HasValue && p.TargetDate < today && !p.Shipping
            );

            return Ok(overdueProductions);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Sunucu hatası: {ex.Message}");
        }
    }

    // POST: api/Production/{id}/archive
    [HttpPost("{id}/archive")]
    public async Task<IActionResult> ArchiveProduction(int id)
    {
        try
        {
            var production = await _productionService.ArchiveProductionAsync(id);
            return Ok(production);
        }
        catch (InvalidOperationException ex)
        {
            return NotFound(ex.Message);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Sunucu hatası: {ex.Message}");
        }
    }

    // POST: api/Production/{id}/unarchive
    [HttpPost("{id}/unarchive")]
    public async Task<IActionResult> UnarchiveProduction(int id)
    {
        try
        {
            var production = await _productionService.UnarchiveProductionAsync(id);
            return Ok(production);
        }
        catch (InvalidOperationException ex)
        {
            return NotFound(ex.Message);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Sunucu hatası: {ex.Message}");
        }
    }

    // GET: api/Production/archived
    [HttpGet("archived")]
    public async Task<ActionResult<IEnumerable<Production>>> GetArchivedProductions()
    {
        try
        {
            var archivedProductions = await _productionService.GetArchivedProductionsAsync();
            return Ok(archivedProductions);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Sunucu hatası: {ex.Message}");
        }
    }

    // GET: api/Production/archived/paginated
    [HttpGet("archived/paginated")]
    public async Task<ActionResult<PaginationDto<Production>>> GetArchivedProductionsPaginated(
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 10,
        [FromQuery] string? searchTerm = null
    )
    {
        try
        {
            var query = new PaginationQuery
            {
                PageNumber = pageNumber,
                PageSize = pageSize,
                SearchTerm = searchTerm,
            };

            var result = await _productionService.GetArchivedProductionsPaginatedAsync(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Sunucu hatası: {ex.Message}");
        }
    }

    // POST: api/Production/{id}/assign
    [HttpPost("{id}/assign")]
    public async Task<IActionResult> AssignTask(int id, [FromBody] string userId)
    {
        try
        {
            var production = await _productionService.AssignTaskAsync(id, userId);

            await _hubContext.Clients.All.SendAsync("ProductionUpdated", production);

            return Ok(production);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Sunucu hatası: {ex.Message}");
        }
    }

    // POST: api/Production/{id}/unassign
    [HttpPost("{id}/unassign")]
    public async Task<IActionResult> UnassignTask(int id)
    {
        try
        {
            var production = await _productionService.UnassignTaskAsync(id);

            await _hubContext.Clients.All.SendAsync("ProductionUpdated", production);

            return Ok(production);
        }
        catch (InvalidOperationException ex)
        {
            return NotFound(ex.Message);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Sunucu hatası: {ex.Message}");
        }
    }

    // GET: api/Production/assigned/{userId}
    [HttpGet("assigned/{userId}")]
    public async Task<ActionResult<IEnumerable<Production>>> GetAssignedTasks(string userId)
    {
        try
        {
            var productions = await _productionService.GetAssignedTasksAsync(userId);
            return Ok(productions);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Sunucu hatası: {ex.Message}");
        }
    }

    // GET: api/Production/technician-stats/{userId}
    [HttpGet("technician-stats/{userId}")]
    public async Task<ActionResult<object>> GetTechnicianStats(string userId)
    {
        try
        {
            var stats = await _productionService.GetTechnicianStatsAsync(userId);
            return Ok(stats);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Sunucu hatası: {ex.Message}");
        }
    }

    // POST: api/Production/{id}/assign-software
    [HttpPost("{id}/assign-software")]
    public async Task<IActionResult> AssignSoftwareTask(int id, [FromBody] string userId)
    {
        try
        {
            var production = await _productionService.AssignSoftwareTaskAsync(id, userId);

            await _hubContext.Clients.All.SendAsync("ProductionUpdated", production);

            return Ok(production);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Sunucu hatası: {ex.Message}");
        }
    }

    // POST: api/Production/{id}/unassign-software
    [HttpPost("{id}/unassign-software")]
    public async Task<IActionResult> UnassignSoftwareTask(int id)
    {
        try
        {
            var production = await _productionService.UnassignSoftwareTaskAsync(id);

            await _hubContext.Clients.All.SendAsync("ProductionUpdated", production);

            return Ok(production);
        }
        catch (InvalidOperationException ex)
        {
            return NotFound(ex.Message);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Sunucu hatası: {ex.Message}");
        }
    }

    // GET: api/Production/software-assigned/{userId}
    [HttpGet("software-assigned/{userId}")]
    public async Task<ActionResult<IEnumerable<Production>>> GetSoftwareAssignedTasks(string userId)
    {
        try
        {
            var productions = await _productionService.GetSoftwareAssignedTasksAsync(userId);
            return Ok(productions);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Sunucu hatası: {ex.Message}");
        }
    }

    // GET: api/Production/software-stats/{userId}
    [HttpGet("software-stats/{userId}")]
    public async Task<ActionResult<object>> GetSoftwareStats(string userId)
    {
        try
        {
            var stats = await _productionService.GetSoftwareStatsAsync(userId);
            return Ok(stats);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Sunucu hatası: {ex.Message}");
        }
    }

    // GET: api/Production/dashboard-stats
    [HttpGet("dashboard-stats")]
    public async Task<ActionResult<object>> GetDashboardStats()
    {
        try
        {
            var stats = await _productionService.GetDashboardStatsAsync();
            return Ok(stats);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Sunucu hatası: {ex.Message}");
        }
    }

    // GET: api/Production/export-archived-excel
    [HttpGet("export-archived-excel")]
    public async Task<IActionResult> ExportArchivedProductionsToExcel()
    {
        try
        {
            var archivedProductions = await _productionService.GetArchivedProductionsAsync();

            using var package = new ExcelPackage();
            var worksheet = package.Workbook.Worksheets.Add("Arşivlenmiş Üretimler");

            // Headers
            worksheet.Cells[1, 1].Value = "ID";
            worksheet.Cells[1, 2].Value = "Ad";
            worksheet.Cells[1, 3].Value = "Soyad";
            worksheet.Cells[1, 4].Value = "P*";
            worksheet.Cells[1, 5].Value = "Yeşil";
            worksheet.Cells[1, 6].Value = "Admin Onayı";
            worksheet.Cells[1, 7].Value = "STL Üretimi";
            worksheet.Cells[1, 8].Value = "Onay Tarihi";
            worksheet.Cells[1, 9].Value = "Hedef Tarihi";
            worksheet.Cells[1, 10].Value = "Çoğaltma";
            worksheet.Cells[1, 11].Value = "Model";
            worksheet.Cells[1, 12].Value = "Plak Basma";
            worksheet.Cells[1, 13].Value = "İnce Kesim";
            worksheet.Cells[1, 14].Value = "Paketleme";
            worksheet.Cells[1, 15].Value = "Kargo";
            worksheet.Cells[1, 16].Value = "Atanan Kullanıcı";

            worksheet.Cells[1, 17].Value = "Açıklama";
            worksheet.Cells[1, 18].Value = "Admin Notu";

            // Header styling
            using (var range = worksheet.Cells[1, 1, 1, 20])
            {
                range.Style.Font.Bold = true;
                range.Style.Fill.PatternType = OfficeOpenXml.Style.ExcelFillStyle.Solid;
                range.Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightGray);
                range.Style.Border.BorderAround(OfficeOpenXml.Style.ExcelBorderStyle.Thin);
            }

            // Get all users for name lookup
            var allUsers = await _userService.GetAllUsersAsync();
            var userLookup = allUsers.ToDictionary(u => u.Id, u => u.FullName ?? u.UserName);

            // Data
            int row = 2;
            foreach (var production in archivedProductions)
            {
                worksheet.Cells[row, 1].Value = production.Id;
                worksheet.Cells[row, 2].Value = production.Name;
                worksheet.Cells[row, 3].Value = production.Surname;
                worksheet.Cells[row, 4].Value = production.PStar ? "Evet" : "Hayır";
                worksheet.Cells[row, 5].Value = production.Greening ? "Evet" : "Hayır";
                worksheet.Cells[row, 6].Value = production.AdminApproval ? "Evet" : "Hayır";
                worksheet.Cells[row, 7].Value = production.StlPrint ? "Evet" : "Hayır";
                worksheet.Cells[row, 8].Value = production.ApprovalDate?.ToString("dd.MM.yyyy");
                worksheet.Cells[row, 9].Value = production.TargetDate?.ToString("dd.MM.yyyy");
                worksheet.Cells[row, 10].Value = production.Replication;
                worksheet.Cells[row, 11].Value = production.Model ? "Evet" : "Hayır";
                worksheet.Cells[row, 12].Value = production.PlatePressing ? "Evet" : "Hayır";
                worksheet.Cells[row, 13].Value = production.FineCut ? "Evet" : "Hayır";
                worksheet.Cells[row, 14].Value = production.Packaging ? "Evet" : "Hayır";
                worksheet.Cells[row, 15].Value = production.Shipping ? "Evet" : "Hayır";

                // Get assigned user name
                var assignedUserName =
                    !string.IsNullOrEmpty(production.AssignedUserId)
                    && userLookup.TryGetValue(production.AssignedUserId, out var assignedName)
                        ? assignedName
                        : string.Empty;
                worksheet.Cells[row, 16].Value = assignedUserName;

                // Get software assigned user name
                var softwareAssignedUserName =
                    !string.IsNullOrEmpty(production.SoftwareAssignedUserId)
                    && userLookup.TryGetValue(
                        production.SoftwareAssignedUserId,
                        out var softwareName
                    )
                        ? softwareName
                        : string.Empty;

                worksheet.Cells[row, 17].Value = production.Notes;
                worksheet.Cells[row, 18].Value = production.AdminNote;
                row++;
            }

            // Auto-fit columns
            worksheet.Cells.AutoFitColumns();

            // Generate file
            var stream = new MemoryStream();
            package.SaveAs(stream);
            stream.Position = 0;

            var fileName = $"Arşivlenmiş_Üretimler_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx";

            return File(
                stream.ToArray(),
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                fileName
            );
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Excel dosyası oluşturulurken hata oluştu: {ex.Message}");
        }
    }

    // GET: api/Production/export-active-excel
    [HttpGet("export-active-excel")]
    public async Task<IActionResult> ExportActiveProductionsToExcel()
    {
        try
        {
            var activeProductions = await _productionService.GetActiveProductionsAsync();

            using var package = new ExcelPackage();
            var worksheet = package.Workbook.Worksheets.Add("Aktif Üretimler");

            // Headers
            worksheet.Cells[1, 1].Value = "ID";
            worksheet.Cells[1, 2].Value = "Ad";
            worksheet.Cells[1, 3].Value = "Soyad";
            worksheet.Cells[1, 4].Value = "P*";
            worksheet.Cells[1, 5].Value = "Yeşil";
            worksheet.Cells[1, 6].Value = "Admin Onayı";
            worksheet.Cells[1, 7].Value = "STL Üretimi";
            worksheet.Cells[1, 8].Value = "Onay Tarihi";
            worksheet.Cells[1, 9].Value = "Hedef Tarihi";
            worksheet.Cells[1, 10].Value = "Çoğaltma";
            worksheet.Cells[1, 11].Value = "Model";
            worksheet.Cells[1, 12].Value = "Plak Basma";
            worksheet.Cells[1, 13].Value = "İnce Kesim";
            worksheet.Cells[1, 14].Value = "Paketleme";
            worksheet.Cells[1, 15].Value = "Kargo";
            worksheet.Cells[1, 16].Value = "Atanan Kullanıcı";
            worksheet.Cells[1, 17].Value = "Açıklama";
            worksheet.Cells[1, 18].Value = "Admin Notu";

            // Header styling
            using (var range = worksheet.Cells[1, 1, 1, 18])
            {
                range.Style.Font.Bold = true;
                range.Style.Fill.PatternType = OfficeOpenXml.Style.ExcelFillStyle.Solid;
                range.Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightGray);
                range.Style.Border.BorderAround(OfficeOpenXml.Style.ExcelBorderStyle.Thin);
            }

            // Get all users for name lookup
            var allUsers = await _userService.GetAllUsersAsync();
            var userLookup = allUsers.ToDictionary(u => u.Id, u => u.FullName ?? u.UserName);

            // Data
            int row = 2;
            foreach (var production in activeProductions)
            {
                worksheet.Cells[row, 1].Value = production.Id;
                worksheet.Cells[row, 2].Value = production.Name;
                worksheet.Cells[row, 3].Value = production.Surname;
                worksheet.Cells[row, 4].Value = production.PStar ? "Evet" : "Hayır";
                worksheet.Cells[row, 5].Value = production.Greening ? "Evet" : "Hayır";
                worksheet.Cells[row, 6].Value = production.AdminApproval ? "Evet" : "Hayır";
                worksheet.Cells[row, 7].Value = production.StlPrint ? "Evet" : "Hayır";
                worksheet.Cells[row, 8].Value = production.ApprovalDate?.ToString("dd.MM.yyyy");
                worksheet.Cells[row, 9].Value = production.TargetDate?.ToString("dd.MM.yyyy");
                worksheet.Cells[row, 10].Value = production.Replication;
                worksheet.Cells[row, 11].Value = production.Model ? "Evet" : "Hayır";
                worksheet.Cells[row, 12].Value = production.PlatePressing ? "Evet" : "Hayır";
                worksheet.Cells[row, 13].Value = production.FineCut ? "Evet" : "Hayır";
                worksheet.Cells[row, 14].Value = production.Packaging ? "Evet" : "Hayır";
                worksheet.Cells[row, 15].Value = production.Shipping ? "Evet" : "Hayır";

                // Get assigned user name
                var assignedUserName =
                    !string.IsNullOrEmpty(production.AssignedUserId)
                    && userLookup.TryGetValue(production.AssignedUserId, out var assignedName)
                        ? assignedName
                        : string.Empty;
                worksheet.Cells[row, 16].Value = assignedUserName;

                worksheet.Cells[row, 17].Value = production.Notes;
                worksheet.Cells[row, 18].Value = production.AdminNote;
                row++;
            }

            // Auto-fit columns
            worksheet.Cells.AutoFitColumns();

            // Generate file
            var stream = new MemoryStream();
            package.SaveAs(stream);
            stream.Position = 0;

            var fileName = $"Aktif_Üretimler_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx";

            return File(
                stream.ToArray(),
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                fileName
            );
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Excel dosyası oluşturulurken hata oluştu: {ex.Message}");
        }
    }

    // POST: api/Production/send-manual-email-reports
    [HttpPost("send-manual-email-reports")]
    public async Task<IActionResult> SendManualEmailReports()
    {
        try
        {
            var success = await _emailService.SendManualReportsAsync();

            if (success)
            {
                return Ok(new { message = "Email raporları başarıyla gönderildi!" });
            }
            else
            {
                return BadRequest(new { message = "Email raporları gönderilemedi!" });
            }
        }
        catch (Exception ex)
        {
            return StatusCode(
                500,
                new { message = $"Email gönderimi sırasında hata oluştu: {ex.Message}" }
            );
        }
    }

    // GET: api/Production/next-email-time
    [HttpGet("next-email-time")]
    public async Task<IActionResult> GetNextEmailTime()
    {
        try
        {
            var nextEmailTime = await _emailService.GetNextScheduledEmailTimeAsync();
            var now = DateTime.Now;
            var remainingTime = nextEmailTime - now;

            return Ok(
                new
                {
                    nextEmailTime = nextEmailTime.ToString("yyyy-MM-dd HH:mm:ss"),
                    remainingSeconds = (int)remainingTime.TotalSeconds,
                    remainingTimeFormatted = FormatTimeSpan(remainingTime),
                }
            );
        }
        catch (Exception ex)
        {
            return StatusCode(
                500,
                new { message = $"Email zamanı alınırken hata oluştu: {ex.Message}" }
            );
        }
    }

    // GET: api/Production/{id}/edit-history/{fieldName}
    [HttpGet("{id}/edit-history/{fieldName}")]
    public async Task<ActionResult<IEnumerable<EditHistory>>> GetEditHistory(
        int id,
        string fieldName
    )
    {
        try
        {
            var editHistory = await _editHistoryService.GetByFieldNameAsync(id, fieldName);
            return Ok(editHistory);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Sunucu hatası: {ex.Message}");
        }
    }

    private string FormatTimeSpan(TimeSpan timeSpan)
    {
        if (timeSpan.TotalDays >= 1)
        {
            return $"{(int)timeSpan.TotalDays} gün {timeSpan.Hours} saat {timeSpan.Minutes} dakika";
        }
        else if (timeSpan.TotalHours >= 1)
        {
            return $"{timeSpan.Hours} saat {timeSpan.Minutes} dakika";
        }
        else if (timeSpan.TotalMinutes >= 1)
        {
            return $"{timeSpan.Minutes} dakika {timeSpan.Seconds} saniye";
        }
        else
        {
            return $"{timeSpan.Seconds} saniye";
        }
    }
}
