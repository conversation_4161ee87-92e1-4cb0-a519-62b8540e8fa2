using System.ComponentModel.DataAnnotations;

namespace Excel.Models;

public class CreateProductionDto
{
    [Required]
    public string Name { get; set; } = default!;
    public string? NameLastEditedBy { get; set; }
    public DateTime? NameLastEditedAt { get; set; }

    [Required]
    public string Surname { get; set; } = default!;
    public string? SurnameLastEditedBy { get; set; }
    public DateTime? SurnameLastEditedAt { get; set; }

    public string? Notes { get; set; }
    public string? NotesLastEditedBy { get; set; }
    public DateTime? NotesLastEditedAt { get; set; }

    public bool PStar { get; set; }
    public string? PStarLastEditedBy { get; set; }
    public DateTime? PStarLastEditedAt { get; set; }

    public bool Greening { get; set; }
    public string? GreeningLastEditedBy { get; set; }
    public DateTime? GreeningLastEditedAt { get; set; }

    public bool AdminApproval { get; set; }
    public string? AdminApprovalLastEditedBy { get; set; }
    public DateTime? AdminApprovalLastEditedAt { get; set; }

    public int Technician { get; set; }
    public string? TechnicianLastEditedBy { get; set; }
    public DateTime? TechnicianLastEditedAt { get; set; }

    public DateOnly? ApprovalDate { get; set; }
    public string? ApprovalDateLastEditedBy { get; set; }
    public DateTime? ApprovalDateLastEditedAt { get; set; }

    public DateOnly? TargetDate { get; set; }
    public string? TargetDateLastEditedBy { get; set; }
    public DateTime? TargetDateLastEditedAt { get; set; }

    public int Replication { get; set; }
    public string? ReplicationLastEditedBy { get; set; }
    public DateTime? ReplicationLastEditedAt { get; set; }

    public bool Model { get; set; }
    public string? ModelLastEditedBy { get; set; }
    public DateTime? ModelLastEditedAt { get; set; }

    public bool PlatePressing { get; set; }
    public string? PlatePressingLastEditedBy { get; set; }
    public DateTime? PlatePressingLastEditedAt { get; set; }

    public bool FineCut { get; set; }
    public string? FineCutLastEditedBy { get; set; }
    public DateTime? FineCutLastEditedAt { get; set; }

    public bool Packaging { get; set; }
    public string? PackagingLastEditedBy { get; set; }
    public DateTime? PackagingLastEditedAt { get; set; }

    public bool Shipping { get; set; }
    public string? ShippingLastEditedBy { get; set; }
    public DateTime? ShippingLastEditedAt { get; set; }

    public bool StlPrint { get; set; }
    public string? StlPrintLastEditedBy { get; set; }
    public DateTime? StlPrintLastEditedAt { get; set; }

    public string? ShippingType { get; set; }
    public string? ShippingTypeLastEditedBy { get; set; }
    public DateTime? ShippingTypeLastEditedAt { get; set; }

    public bool IsArchived { get; set; }
    public string? IsArchivedLastEditedBy { get; set; }
    public DateTime? IsArchivedLastEditedAt { get; set; }

    public string? AdminNote { get; set; }
    public string? AdminNoteLastEditedBy { get; set; }
    public DateTime? AdminNoteLastEditedAt { get; set; }

    // Software Task Assignment Fields
    public string? SoftwareAssignedUserId { get; set; }
    public DateTime? SoftwareAssignedAt { get; set; }
    public DateTime? SoftwareStartedAt { get; set; }
    public DateTime? SoftwareCompletedAt { get; set; }
}
