using AutoMapper;
using Excel.Data.Repositories;
using Excel.Models;
using Excel.Services.Interfaces;

namespace Excel.Services;

public class ProductionService : IProductionService
{
    private readonly IProductionRepository _productionRepository;
    private readonly IMapper _mapper;
    private readonly IEditHistoryService _editHistoryService;

    public ProductionService(
        IProductionRepository productionRepository,
        IMapper mapper,
        IEditHistoryService editHistoryService
    )
    {
        _productionRepository = productionRepository;
        _mapper = mapper;
        _editHistoryService = editHistoryService;
    }

    public async Task<IEnumerable<Production>> GetAllProductionsAsync()
    {
        return await _productionRepository.GetAllAsync();
    }

    public async Task<Production?> GetProductionByIdAsync(int id)
    {
        return await _productionRepository.GetByIdAsync(id);
    }

    public async Task<Production> CreateProductionAsync(CreateProductionDto dto)
    {
        var production = _mapper.Map<Production>(dto);
        return await _productionRepository.AddAsync(production);
    }

    public async Task<Production> UpdateProductionAsync(
        int id,
        CreateProductionDto dto,
        string? userId
    )
    {
        var production = await _productionRepository.GetByIdAsync(id);
        if (production == null)
            throw new InvalidOperationException("Üretim bulunamadı");

        if (production.PStar != dto.PStar)
        {
            production.PStarLastEditedBy = userId;
            production.PStarLastEditedAt = DateTime.UtcNow;
            await _editHistoryService.AddEditHistoryAsync(
                id,
                "PStar",
                production.PStar.ToString(),
                dto.PStar.ToString(),
                userId ?? "Unknown"
            );
        }
        if (production.Greening != dto.Greening)
        {
            production.GreeningLastEditedBy = userId;
            production.GreeningLastEditedAt = DateTime.UtcNow;
            await _editHistoryService.AddEditHistoryAsync(
                id,
                "Greening",
                production.Greening.ToString(),
                dto.Greening.ToString(),
                userId ?? "Unknown"
            );
        }
        if (production.AdminApproval != dto.AdminApproval)
        {
            production.AdminApprovalLastEditedBy = userId;
            production.AdminApprovalLastEditedAt = DateTime.UtcNow;
            await _editHistoryService.AddEditHistoryAsync(
                id,
                "AdminApproval",
                production.AdminApproval.ToString(),
                dto.AdminApproval.ToString(),
                userId ?? "Unknown"
            );
        }
        if (production.StlPrint != dto.StlPrint)
        {
            production.StlPrintLastEditedBy = userId;
            production.StlPrintLastEditedAt = DateTime.UtcNow;
            await _editHistoryService.AddEditHistoryAsync(
                id,
                "StlPrint",
                production.StlPrint.ToString(),
                dto.StlPrint.ToString(),
                userId ?? "Unknown"
            );
        }
        if (production.Name != dto.Name)
        {
            production.NameLastEditedBy = userId;
            production.NameLastEditedAt = DateTime.UtcNow;
            await _editHistoryService.AddEditHistoryAsync(
                id,
                "Name",
                production.Name,
                dto.Name,
                userId ?? "Unknown"
            );
        }
        if (production.Surname != dto.Surname)
        {
            production.SurnameLastEditedBy = userId;
            production.SurnameLastEditedAt = DateTime.UtcNow;
            await _editHistoryService.AddEditHistoryAsync(
                id,
                "Surname",
                production.Surname,
                dto.Surname,
                userId ?? "Unknown"
            );
        }
        if (production.Notes != dto.Notes)
        {
            production.NotesLastEditedBy = userId;
            production.NotesLastEditedAt = DateTime.UtcNow;
            await _editHistoryService.AddEditHistoryAsync(
                id,
                "Notes",
                production.Notes,
                dto.Notes,
                userId ?? "Unknown"
            );
        }
        if (production.Technician != dto.Technician)
        {
            production.TechnicianLastEditedBy = userId;
            production.TechnicianLastEditedAt = DateTime.UtcNow;
            await _editHistoryService.AddEditHistoryAsync(
                id,
                "Technician",
                production.Technician.ToString(),
                dto.Technician.ToString(),
                userId ?? "Unknown"
            );
        }
        if (production.ApprovalDate != dto.ApprovalDate)
        {
            production.ApprovalDateLastEditedBy = userId;
            production.ApprovalDateLastEditedAt = DateTime.UtcNow;
            await _editHistoryService.AddEditHistoryAsync(
                id,
                "ApprovalDate",
                production.ApprovalDate?.ToString(),
                dto.ApprovalDate?.ToString(),
                userId ?? "Unknown"
            );
        }
        if (production.TargetDate != dto.TargetDate)
        {
            production.TargetDateLastEditedBy = userId;
            production.TargetDateLastEditedAt = DateTime.UtcNow;
            await _editHistoryService.AddEditHistoryAsync(
                id,
                "TargetDate",
                production.TargetDate?.ToString(),
                dto.TargetDate?.ToString(),
                userId ?? "Unknown"
            );
        }
        if (production.Replication != dto.Replication)
        {
            production.ReplicationLastEditedBy = userId;
            production.ReplicationLastEditedAt = DateTime.UtcNow;
            await _editHistoryService.AddEditHistoryAsync(
                id,
                "Replication",
                production.Replication.ToString(),
                dto.Replication.ToString(),
                userId ?? "Unknown"
            );
        }
        if (production.Model != dto.Model)
        {
            production.ModelLastEditedBy = userId;
            production.ModelLastEditedAt = DateTime.UtcNow;
            await _editHistoryService.AddEditHistoryAsync(
                id,
                "Model",
                production.Model.ToString(),
                dto.Model.ToString(),
                userId ?? "Unknown"
            );
        }
        if (production.PlatePressing != dto.PlatePressing)
        {
            production.PlatePressingLastEditedBy = userId;
            production.PlatePressingLastEditedAt = DateTime.UtcNow;
            await _editHistoryService.AddEditHistoryAsync(
                id,
                "PlatePressing",
                production.PlatePressing.ToString(),
                dto.PlatePressing.ToString(),
                userId ?? "Unknown"
            );
        }
        if (production.FineCut != dto.FineCut)
        {
            production.FineCutLastEditedBy = userId;
            production.FineCutLastEditedAt = DateTime.UtcNow;
            await _editHistoryService.AddEditHistoryAsync(
                id,
                "FineCut",
                production.FineCut.ToString(),
                dto.FineCut.ToString(),
                userId ?? "Unknown"
            );
        }
        if (production.Packaging != dto.Packaging)
        {
            production.PackagingLastEditedBy = userId;
            production.PackagingLastEditedAt = DateTime.UtcNow;
            await _editHistoryService.AddEditHistoryAsync(
                id,
                "Packaging",
                production.Packaging.ToString(),
                dto.Packaging.ToString(),
                userId ?? "Unknown"
            );
        }
        if (production.Shipping != dto.Shipping)
        {
            production.ShippingLastEditedBy = userId;
            production.ShippingLastEditedAt = DateTime.UtcNow;
            await _editHistoryService.AddEditHistoryAsync(
                id,
                "Shipping",
                production.Shipping.ToString(),
                dto.Shipping.ToString(),
                userId ?? "Unknown"
            );
        }
        if (production.ShippingType != dto.ShippingType)
        {
            production.ShippingTypeLastEditedBy = userId;
            production.ShippingTypeLastEditedAt = DateTime.UtcNow;
            await _editHistoryService.AddEditHistoryAsync(
                id,
                "ShippingType",
                production.ShippingType,
                dto.ShippingType,
                userId ?? "Unknown"
            );
        }
        if (production.IsArchived != dto.IsArchived)
        {
            production.IsArchivedLastEditedBy = userId;
            production.IsArchivedLastEditedAt = DateTime.UtcNow;
            await _editHistoryService.AddEditHistoryAsync(
                id,
                "IsArchived",
                production.IsArchived.ToString(),
                dto.IsArchived.ToString(),
                userId ?? "Unknown"
            );
        }
        if (production.AdminNote != dto.AdminNote)
        {
            production.AdminNoteLastEditedBy = userId;
            production.AdminNoteLastEditedAt = DateTime.UtcNow;
            await _editHistoryService.AddEditHistoryAsync(
                id,
                "AdminNote",
                production.AdminNote,
                dto.AdminNote,
                userId ?? "Unknown"
            );
        }

        production.PStar = dto.PStar;
        production.Greening = dto.Greening;
        production.AdminApproval = dto.AdminApproval;
        production.StlPrint = dto.StlPrint;
        production.Name = dto.Name;
        production.Surname = dto.Surname;
        production.Notes = dto.Notes;
        production.Technician = dto.Technician;
        production.ApprovalDate = dto.ApprovalDate;
        production.TargetDate = dto.TargetDate;
        production.Replication = dto.Replication;
        production.Model = dto.Model;
        production.PlatePressing = dto.PlatePressing;
        production.FineCut = dto.FineCut;
        production.Packaging = dto.Packaging;
        production.Shipping = dto.Shipping;
        production.ShippingType = dto.ShippingType;
        production.IsArchived = dto.IsArchived;
        production.AdminNote = dto.AdminNote;

        return await _productionRepository.UpdateAsync(production);
    }

    public async Task DeleteProductionAsync(int id)
    {
        var production = await _productionRepository.GetByIdAsync(id);
        if (production == null)
            throw new InvalidOperationException("Üretim bulunamadı");

        await _productionRepository.DeleteAsync(production);
    }

    public async Task<IEnumerable<Production>> GetActiveProductionsAsync()
    {
        return await _productionRepository.GetActiveProductionsAsync();
    }

    public async Task<IEnumerable<Production>> GetArchivedProductionsAsync()
    {
        return await _productionRepository.GetArchivedProductionsAsync();
    }

    public async Task<PaginationDto<Production>> GetArchivedProductionsPaginatedAsync(
        PaginationQuery query
    )
    {
        return await _productionRepository.GetArchivedProductionsPaginatedAsync(query);
    }

    public async Task<IEnumerable<Production>> GetProductionsByDateRangeAsync(
        DateTime startDate,
        DateTime endDate
    )
    {
        return await _productionRepository.GetProductionsByDateRangeAsync(startDate, endDate);
    }

    public async Task<IEnumerable<Production>> GetProductionsByStatusAsync(string status)
    {
        return await _productionRepository.GetProductionsByStatusAsync(status);
    }

    public async Task<int> GetActiveProductionsCountAsync()
    {
        return await _productionRepository.GetActiveProductionsCountAsync();
    }

    public async Task<Production> ArchiveProductionAsync(int id)
    {
        return await _productionRepository.ArchiveProductionAsync(id);
    }

    public async Task<Production> UnarchiveProductionAsync(int id)
    {
        return await _productionRepository.UnarchiveProductionAsync(id);
    }

    public async Task<Production> AssignTaskAsync(int productionId, string userId)
    {
        return await _productionRepository.AssignTaskAsync(productionId, userId);
    }

    public async Task<Production> UnassignTaskAsync(int productionId)
    {
        return await _productionRepository.UnassignTaskAsync(productionId);
    }

    public async Task<IEnumerable<Production>> GetAssignedTasksAsync(string userId)
    {
        return await _productionRepository.GetAssignedTasksAsync(userId);
    }

    public async Task<object> GetTechnicianStatsAsync(string userId)
    {
        var assignedTasks = await _productionRepository.GetAssignedTasksAsync(userId);
        var completedTasks = assignedTasks.Where(p => p.CompletedAt.HasValue);

        var averageCompletionTime = completedTasks.Any()
            ? completedTasks.Average(p =>
                p.CompletedAt.HasValue && p.AssignedAt.HasValue
                    ? (p.CompletedAt.Value - p.AssignedAt.Value).TotalDays
                    : 0
            )
            : 0;

        return new
        {
            AssignedTasks = assignedTasks.Count(),
            CompletedTasks = completedTasks.Count(),
            InProgressTasks = assignedTasks.Count(p => !p.CompletedAt.HasValue),
            AverageCompletionDays = Math.Round(averageCompletionTime, 2),
            OnTimeCompletions = completedTasks.Count(p =>
                p.TargetDate.HasValue
                && p.CompletedAt.HasValue
                && p.CompletedAt.Value <= p.TargetDate.Value.ToDateTime(TimeOnly.MinValue)
            ),
            LateCompletions = completedTasks.Count(p =>
                p.TargetDate.HasValue
                && p.CompletedAt.HasValue
                && p.CompletedAt.Value > p.TargetDate.Value.ToDateTime(TimeOnly.MinValue)
            ),
        };
    }

    public async Task<object> GetDashboardStatsAsync()
    {
        var allProductions = await _productionRepository.GetActiveProductionsAsync();
        var assignedTasks = allProductions.Where(p => !string.IsNullOrEmpty(p.AssignedUserId));
        var unassignedTasks = allProductions.Where(p => string.IsNullOrEmpty(p.AssignedUserId));
        var completedTasks = allProductions.Where(p => p.CompletedAt.HasValue);

        // Teknisyen performans istatistikleri
        var technicianStats = assignedTasks
            .GroupBy(p => p.AssignedUserId)
            .Select(g => new
            {
                TechnicianId = g.Key,
                TaskCount = g.Count(),
                CompletedCount = g.Count(p => p.CompletedAt.HasValue),
                AverageCompletionDays = g.Where(p =>
                        p.CompletedAt.HasValue && p.AssignedAt.HasValue
                    )
                    .Average(p => (p.CompletedAt!.Value - p.AssignedAt!.Value).TotalDays),
            })
            .ToList();

        return new
        {
            TotalTasks = allProductions.Count(),
            AssignedTasks = assignedTasks.Count(),
            UnassignedTasks = unassignedTasks.Count(),
            CompletedTasks = completedTasks.Count(),
            TechnicianStats = technicianStats,
            TasksByStatus = allProductions
                .GroupBy(p => p.Status)
                .Select(g => new { Status = g.Key, Count = g.Count() }),
            OverdueTasks = allProductions.Count(p =>
                p.TargetDate.HasValue
                && p.TargetDate.Value < DateOnly.FromDateTime(DateTime.Now)
                && !p.CompletedAt.HasValue
            ),
        };
    }

    public async Task<Production> AssignSoftwareTaskAsync(int productionId, string userId)
    {
        return await _productionRepository.AssignSoftwareTaskAsync(productionId, userId);
    }

    public async Task<Production> UnassignSoftwareTaskAsync(int productionId)
    {
        return await _productionRepository.UnassignSoftwareTaskAsync(productionId);
    }

    public async Task<IEnumerable<Production>> GetSoftwareAssignedTasksAsync(string userId)
    {
        return await _productionRepository.GetSoftwareAssignedTasksAsync(userId);
    }

    public async Task<object> GetSoftwareStatsAsync(string userId)
    {
        var assignedTasks = await _productionRepository.GetSoftwareAssignedTasksAsync(userId);
        var completedTasks = assignedTasks.Where(p => p.SoftwareCompletedAt.HasValue);

        var averageCompletionTime = completedTasks.Any()
            ? completedTasks.Average(p =>
                p.SoftwareCompletedAt.HasValue && p.SoftwareAssignedAt.HasValue
                    ? (p.SoftwareCompletedAt.Value - p.SoftwareAssignedAt.Value).TotalDays
                    : 0
            )
            : 0;

        return new
        {
            TotalAssigned = assignedTasks.Count(),
            Completed = completedTasks.Count(),
            InProgress = assignedTasks.Count(p => !p.SoftwareCompletedAt.HasValue),
            AverageCompletionTime = averageCompletionTime,
            OnTimeCompletions = completedTasks.Count(p =>
                p.TargetDate.HasValue
                && p.SoftwareCompletedAt.HasValue
                && p.SoftwareCompletedAt.Value <= p.TargetDate.Value.ToDateTime(TimeOnly.MinValue)
            ),
            LateCompletions = completedTasks.Count(p =>
                p.TargetDate.HasValue
                && p.SoftwareCompletedAt.HasValue
                && p.SoftwareCompletedAt.Value > p.TargetDate.Value.ToDateTime(TimeOnly.MinValue)
            ),
        };
    }
}
