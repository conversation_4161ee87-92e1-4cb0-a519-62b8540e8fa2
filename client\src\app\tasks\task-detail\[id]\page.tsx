"use client";

import { useEffect, useState, useCallback } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";
import {
  ArrowLeft,
  Calendar,
  User,
  FileText,
  CheckCircle,
  XCircle,
  UserCheck,
  UserX,
  Save,
  AlertCircle,
  Clock,
  Package,
  Wrench,
  Scissors,
  Archive,
  Truck,
  // Play,
  // Pause,
  // RefreshCw,
  X,
} from "lucide-react";
import { useAuthStore } from "@/stores/authStore";
import { useSignalR, useSignalREvent } from "@/hooks/useSignalR";
import { apiService } from "@/lib/api";

interface Production {
  id: number;
  pStar: boolean;
  greening: boolean;
  adminApproval: boolean;
  name: string;
  surname: string;
  notes?: string;
  technician: number;
  approvalDate?: string;
  targetDate?: string;
  replication: number;
  model: boolean;
  platePressing: boolean;
  fineCut: boolean;
  packaging: boolean;
  shipping: boolean;
  shippingType?: string;
  isArchived?: boolean;
  assignedUserId?: string;
  assignedAt?: string;
  startedAt?: string;
  completedAt?: string;
  isAssigned?: boolean;
  softwareAssignedUserId?: string;
  softwareAssignedAt?: string;
  softwareStartedAt?: string;
  softwareCompletedAt?: string;
  isSoftwareAssigned?: boolean;
  adminNote?: string;
  createdAt?: string;
  status?: string;
}

const TaskDetailPage = () => {
  const params = useParams();
  const router = useRouter();
  const { user } = useAuthStore();
  const [production, setProduction] = useState<Production | null>(null);
  const [loading, setLoading] = useState(true);
  const [updating, setUpdating] = useState(false);
  const [editingNotes, setEditingNotes] = useState(false);
  const [notesValue, setNotesValue] = useState("");
  const [editingReplication, setEditingReplication] = useState(false);
  const [replicationValue, setReplicationValue] = useState("");
  // const [isAutoRefreshEnabled, setIsAutoRefreshEnabled] = useState(true);
  // const [countdown, setCountdown] = useState(60);
  // const [lastUpdated, setLastUpdated] = useState<Date>(new Date());
  const [showNotesModal, setShowNotesModal] = useState(false);
  const [showAdminNoteModal, setShowAdminNoteModal] = useState(false);

  const taskId = params.id as string;

  // Initialize SignalR connection
  useSignalR();

  const calculateRemainingDays = useCallback((targetDate?: string) => {
    if (!targetDate) return null;
    const target = new Date(targetDate);
    const today = new Date();
    const diffTime = target.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  }, []);

  const fetchProduction = useCallback(async () => {
    try {
      setLoading(true);
      const data = await apiService.getProduction(parseInt(taskId));
      setProduction(data);
      setNotesValue(data.notes || "");
    } catch (error) {
      console.error("Error fetching production:", error);
      toast.error("Görev bulunamadı!");
      router.push("/tasks/uretim");
    } finally {
      setLoading(false);
    }
  }, [taskId, router]);

  const updateProduction = async (id: number, updates: Partial<Production>) => {
    try {
      setUpdating(true);

      if (!production) return false;

      const allowedFields = [
        "id",
        "pStar",
        "greening",
        "adminApproval",
        "name",
        "surname",
        "notes",
        "technician",
        "approvalDate",
        "targetDate",
        "replication",
        "model",
        "platePressing",
        "fineCut",
        "packaging",
        "shipping",
        "shippingType",
        "isArchived",
        "adminNote",
        "assignedUserId",
        "assignedAt",
        "startedAt",
        "completedAt",
        "softwareAssignedUserId",
        "softwareAssignedAt",
        "softwareStartedAt",
        "softwareCompletedAt",
      ];

      const updatedProduction: Record<string, unknown> = {};
      for (const key of allowedFields) {
        if (key in updates) {
          updatedProduction[key] = (
            updates as unknown as Record<string, unknown>
          )[key];
        } else if (key in production) {
          updatedProduction[key] = (
            production as unknown as Record<string, unknown>
          )[key];
        }
      }

      const updatedProductionData = await apiService.updateProduction(
        id,
        updatedProduction
      );
      setProduction(updatedProductionData);
      return true;
    } catch (error) {
      console.error("Error updating production:", error);
    } finally {
      setUpdating(false);
    }
    return false;
  };

  const assignTask = async (productionId: number) => {
    try {
      const updatedProduction = await apiService.assignProduction(
        productionId,
        user?.id || ""
      );
      setProduction(updatedProduction);
      toast.success("Görev başarıyla alındı!");
      return true;
    } catch (error) {
      console.error("Error assigning task:", error);
      toast.error("Görev alınamadı!");
    }
    return false;
  };

  const unassignTask = async (productionId: number) => {
    try {
      const updatedProduction = await apiService.unassignProduction(
        productionId
      );
      setProduction(updatedProduction);
      toast.success("Görev başarıyla bırakıldı!");
      return true;
    } catch (error) {
      console.error("Error unassigning task:", error);
      toast.error("Görev bırakılamadı!");
    }
    return false;
  };

  const toggleProcessStep = async (field: keyof Production, value: boolean) => {
    const success = await updateProduction(production!.id, { [field]: value });
    if (success) {
      toast.success(
        `${getProcessStepName(field)} ${value ? "tamamlandı" : "iptal edildi"}!`
      );
    } else {
      toast.error("Güncelleme başarısız!");
    }
  };

  const getProcessStepName = (field: keyof Production) => {
    const stepNames = {
      model: "Model",
      platePressing: "Plak Basma",
      fineCut: "İnce Kesim",
      packaging: "Paketleme",
      shipping: "Kargo",
    };
    return stepNames[field as keyof typeof stepNames] || field;
  };

  const saveNotes = async () => {
    const success = await updateProduction(production!.id, {
      notes: notesValue,
    });
    if (success) {
      toast.success("Açıklama kaydedildi!");
      setEditingNotes(false);
    } else {
      toast.error("Açıklama kaydedilemedi!");
    }
  };

  const cancelNotes = () => {
    setNotesValue(production?.notes || "");
    setEditingNotes(false);
  };

  const handleReplicationSave = async () => {
    const value = parseInt(replicationValue) || 0;
    const success = await updateProduction(production!.id, {
      replication: value,
    });
    if (success) {
      toast.success("Çoğaltma başarıyla güncellendi!");
      setEditingReplication(false);
    } else {
      toast.error("Çoğaltma güncellenemedi!");
    }
  };
  const handleReplicationCancel = () => {
    setEditingReplication(false);
    setReplicationValue(production?.replication.toString() || "0");
  };

  // SignalR event handlers
  useSignalREvent<Production>("ProductionUpdated", (updatedProduction) => {
    if (updatedProduction.id === production?.id) {
      setProduction(updatedProduction);
    }
  });

  useSignalREvent<number>("ProductionDeleted", (productionId) => {
    if (productionId === production?.id) {
      toast.info("Görev silindi");
      router.push("/tasks/uretim");
    }
  });

  // useEffect(() => {
  //   let intervalId: NodeJS.Timeout;
  //   if (isAutoRefreshEnabled && countdown > 0) {
  //     intervalId = setInterval(() => {
  //       setCountdown((prev) => prev - 1);
  //     }, 1000);
  //   } else if (isAutoRefreshEnabled && countdown === 0) {
  //     handleRefresh();
  //   }
  //   return () => {
  //     if (intervalId) clearInterval(intervalId);
  //   };
  // }, [isAutoRefreshEnabled, countdown]);

  // const handleRefresh = useCallback(async () => {
  //   await fetchProduction();
  //   setLastUpdated(new Date());
  //   setCountdown(60);
  // }, [fetchProduction]);

  // const toggleAutoRefresh = () => {
  //   setIsAutoRefreshEnabled((prev) => {
  //     if (!prev) setCountdown(60);
  //     return !prev;
  //   });
  // };

  // const formatLastUpdated = (date: Date) => {
  //   return date.toLocaleTimeString("tr-TR", {
  //     hour: "2-digit",
  //     minute: "2-digit",
  //     second: "2-digit",
  //   });
  // };

  useEffect(() => {
    fetchProduction();
  }, [fetchProduction]);

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-600">Görev yükleniyor...</p>
        </div>
      </div>
    );
  }

  if (!production) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="text-center">
          <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <p className="text-gray-600">Görev bulunamadı</p>
          <Button onClick={() => router.push("/tasks/uretim")} className="mt-4">
            Geri Dön
          </Button>
        </div>
      </div>
    );
  }

  const remainingDays = calculateRemainingDays(production.targetDate);
  const isAssignedToMe = production.assignedUserId === user?.id;
  const canEdit = user?.role === "Technician" && isAssignedToMe;

  return (
    <div className="min-h-screen bg-gray-50 pb-20">
      {/* Header */}
      <div className="bg-white shadow-sm border-b sticky top-0 z-10">
        <div className="px-4 py-3 flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => router.push("/tasks/uretim")}
              className="p-2"
            >
              <ArrowLeft className="h-5 w-5" />
            </Button>
            <div>
              <h1 className="text-lg font-semibold text-gray-900">
                Görev Detayı
              </h1>
              <p className="text-sm  font-semibold">
                #{production.name} {production.surname}
              </p>
            </div>
          </div>

          {/* Task Assignment Button */}
          {user?.role === "Technician" && (
            <div>
              {isAssignedToMe ? (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => unassignTask(production.id)}
                  className="text-red-600 border-red-200 hover:bg-red-50"
                >
                  <UserX className="h-4 w-4 mr-2" />
                  Bırak
                </Button>
              ) : !production.assignedUserId ? (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => assignTask(production.id)}
                  className="text-green-600 border-green-200 hover:bg-green-50"
                >
                  <UserCheck className="h-4 w-4 mr-2" />
                  Al
                </Button>
              ) : (
                <Badge variant="secondary" className="text-xs">
                  Atanmış
                </Badge>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Auto Refresh Widget */}
      {/* <Card className="border-0 shadow-sm px-3 py-4 mt-2">
        <div className="flex items-center gap-3">
          <Button
            variant="outline"
            size="sm"
            onClick={toggleAutoRefresh}
            className="h-8 w-8 p-0"
          >
            {isAutoRefreshEnabled ? (
              <Pause className="h-4 w-4" />
            ) : (
              <Play className="h-4 w-4" />
            )}
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            className="h-8 w-8 p-0"
            disabled={loading}
          >
            <RefreshCw className={`h-4 w-4 ${loading ? "animate-spin" : ""}`} />
          </Button>
          <div className="flex flex-col items-center">
            <div className="flex items-center gap-1 text-xs text-gray-600">
              <Clock className="h-3 w-3" />
              {isAutoRefreshEnabled ? (
                <span>Yenileme: {countdown}s</span>
              ) : (
                <span>Duraklatıldı</span>
              )}
            </div>
            <div className="text-xs text-gray-500">
              Son: {formatLastUpdated(lastUpdated)}
            </div>
          </div>
          {isAutoRefreshEnabled && (
            <div className="w-16 h-1 bg-gray-200 rounded-full overflow-hidden">
              <div
                className="h-full bg-blue-500 transition-all duration-1000 ease-linear"
                style={{ width: `${((60 - countdown) / 60) * 100}%` }}
              />
            </div>
          )}
        </div>
      </Card> */}

      <div className=" space-y-6 mt-5 ">
        {/* Patient Info */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg flex items-center gap-2">
              <User className="h-5 w-5" />
              Hasta Bilgileri
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex gap-1 items-center ">
              <p className="text-md text-gray-600 ">Ad Soyad:</p>
              <p className="text-lg font-semibold text-gray-900">
                {production.name} {production.surname}
              </p>
            </div>

            <div className="flex gap-1 items-center ">
              <p className="text-md text-gray-600 ">Kalan Gün:</p>
              {remainingDays !== null ? (
                <Badge
                  variant={
                    remainingDays < 0
                      ? "destructive"
                      : remainingDays <= 2
                      ? "secondary"
                      : "default"
                  }
                  className={`text-xs ${
                    remainingDays <= 2 && remainingDays >= 0
                      ? "animate-pulse bg-red-500 text-white"
                      : ""
                  }`}
                >
                  <Clock className="h-3 w-3 mr-1" />
                  {remainingDays > 0
                    ? `${remainingDays} gün`
                    : remainingDays === 0
                    ? "Bugün"
                    : `-${Math.abs(remainingDays)} gün`}
                </Badge>
              ) : (
                <Badge variant="outline" className="text-sm">
                  Tarih belirtilmemiş
                </Badge>
              )}
            </div>

            {/* Replication Editable */}
            <div className="flex gap-1 items-center">
              <p className="text-md text-gray-600  flex items-center gap-2">
                Çoğaltma:
              </p>
              {canEdit && editingReplication ? (
                <div className="flex gap-2 items-center justify-between">
                  <input
                    type="number"
                    min={0}
                    value={replicationValue}
                    onChange={(e) => setReplicationValue(e.target.value)}
                    className="w-20 px-2 py-1 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-transparent text-lg font-semibold"
                  />
                  <div className="flex gap-2">
                    <Button
                      size="sm"
                      onClick={handleReplicationSave}
                      disabled={updating}
                    >
                      Kaydet
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={handleReplicationCancel}
                      disabled={updating}
                    >
                      İptal
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="flex items-center gap-2 justify-between  w-full ">
                  <p className="text-lg font-semibold text-gray-900">
                    {production.replication}
                  </p>
                  {canEdit && (
                    <Button
                      size="sm"
                      variant="outline"
                      className=""
                      onClick={() => {
                        setEditingReplication(true);
                        setReplicationValue(production.replication.toString());
                      }}
                    >
                      Düzenle
                    </Button>
                  )}
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Admin Note Section */}
        {production.adminNote && (
          <Card className="border-2 border-orange-300 bg-orange-50">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Not
                <span className="text-red-500 font-bold text-xl">!</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              {editingNotes ? (
                <div className="space-y-4">
                  <textarea
                    value={notesValue}
                    onChange={(e) => setNotesValue(e.target.value)}
                    className="w-full h-32 p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none  "
                    placeholder="Admin notu ekleyin..."
                  />
                  <div className="flex gap-2">
                    <Button
                      onClick={saveNotes}
                      disabled={updating}
                      className="flex-1"
                    >
                      <Save className="h-4 w-4 mr-2" />
                      Kaydet
                    </Button>
                    <Button
                      variant="outline"
                      onClick={cancelNotes}
                      disabled={updating}
                      className="flex-1"
                    >
                      İptal
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="space-y-4">
                  <div
                    className="min-h-[100px] p-3 rounded-lg cursor-pointer transition-colors bg-orange-100 hover:bg-orange-200 border border-orange-300"
                    onClick={() => setShowAdminNoteModal(true)}
                  >
                    <p
                      className="text-sm whitespace-pre-wrap overflow-hidden break-words text-gray-800 font-medium"
                      style={{
                        display: "-webkit-box",
                        WebkitLineClamp: 6,
                        WebkitBoxOrient: "vertical",
                        textOverflow: "ellipsis",
                      }}
                    >
                      {production.adminNote}
                    </p>
                  </div>
                  {/* {canEdit && (
                    <Button
                      variant="outline"
                      onClick={() => setEditingNotes(true)}
                      className="w-full"
                    >
                      <FileText className="h-4 w-4 mr-2" />
                      Açıklama Düzenle
                    </Button>
                  )} */}
                </div>
              )}
            </CardContent>
          </Card>
        )}
        {/* Notes Section */}
        {production.notes && (
          <Card className="border-2  bg-gray-50">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Açıklama
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div
                  className="min-h-[100px] p-3 rounded-lg cursor-pointer transition-colors bg-gray-50    "
                  onClick={() => setShowNotesModal(true)}
                >
                  <p
                    className="text-sm whitespace-pre-wrap overflow-hidden break-words text-gray-800"
                    style={{
                      display: "-webkit-box",
                      WebkitLineClamp: 6,
                      WebkitBoxOrient: "vertical",
                      textOverflow: "ellipsis",
                    }}
                  >
                    {production.notes}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Process Tracking */}
        <Card>
          <CardHeader className="pb-4">
            <CardTitle className="text-lg flex items-center gap-2">
              <Package className="h-5 w-5" />
              Süreç Takibi
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {[
              { field: "model", label: "Model", icon: Wrench },
              { field: "platePressing", label: "Plak Basma", icon: Archive },
              { field: "fineCut", label: "İnce Kesim", icon: Scissors },
              { field: "packaging", label: "Paketleme", icon: Package },
              { field: "shipping", label: "Kargo", icon: Truck },
            ].map(({ field, label, icon: Icon }) => (
              <div
                key={field}
                className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
              >
                <div className="flex items-center gap-3">
                  <Icon className="h-4 w-4 text-gray-600" />
                  <span className="text-sm font-medium text-gray-900">
                    {label}
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <Badge
                    variant={
                      (production[field as keyof Production] as boolean)
                        ? "default"
                        : "secondary"
                    }
                    className={`text-xs ${
                      (production[field as keyof Production] as boolean)
                        ? "bg-green-600"
                        : "bg-amber-300"
                    }`}
                  >
                    {(production[field as keyof Production] as boolean)
                      ? "Tamamlandı"
                      : "Bekliyor"}
                  </Badge>
                  {(field === "model"
                    ? user?.role === "Technician"
                    : canEdit) && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() =>
                        toggleProcessStep(
                          field as keyof Production,
                          !(production[field as keyof Production] as boolean)
                        )
                      }
                      disabled={updating}
                      className="h-8 w-8 p-0"
                    >
                      {(production[field as keyof Production] as boolean) ? (
                        <XCircle className="h-4 w-4 text-red-600" />
                      ) : (
                        <CheckCircle className="h-4 w-4 text-amber-600" />
                      )}
                    </Button>
                  )}
                </div>
              </div>
            ))}
          </CardContent>
        </Card>

        {/* Status Info */}
        <Card>
          <CardHeader className="pb-4">
            <CardTitle className="text-lg flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              Durum Bilgisi
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Durum</span>
              <Badge variant="outline" className="text-xs">
                {production.status === "Assigned" ? "Atanmış" : "Bekliyor"}
              </Badge>
            </div>

            {production.targetDate && (
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Hedef Tarih</span>
                <span className="text-sm font-medium text-gray-900">
                  {new Date(production.targetDate).toLocaleDateString("tr-TR")}
                </span>
              </div>
            )}

            {production.assignedUserId && (
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Atanan Teknisyen</span>
                <Badge variant="default" className="text-xs">
                  {isAssignedToMe ? "Ben" : "Atanmış"}
                </Badge>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Notes Modal */}
      {showNotesModal && (
        <div className="fixed inset-0 bg-black/50 bg-opacity-50 flex items-center justify-center z-50">
          {/* Backdrop for outside click */}
          <div
            className="absolute inset-0"
            onClick={() => setShowNotesModal(false)}
          />
          <div
            className={`bg-white rounded-lg shadow-lg p-6 w-full max-w-2xl mx-4 relative z-10 ${
              production.notes ? "border-2 border-blue-300" : ""
            }`}
            onClick={(e) => e.stopPropagation()}
          >
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Notlar Detayı
                {production.notes && (
                  <span className="text-blue-500 font-bold text-xl">!</span>
                )}
              </h3>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowNotesModal(false)}
                className="h-8 w-8 p-0"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
            <div className="overflow-x-auto">
              <p className="text-base text-gray-800 whitespace-pre-wrap break-words font-medium leading-relaxed">
                {production?.notes || "Not bulunmuyor"}
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Admin Note Modal */}
      {showAdminNoteModal && (
        <div className="fixed inset-0 bg-black/50 bg-opacity-50 flex items-center justify-center z-50">
          {/* Backdrop for outside click */}
          <div
            className="absolute inset-0"
            onClick={() => setShowAdminNoteModal(false)}
          />
          <div
            className={`bg-white rounded-lg shadow-lg p-6 w-full max-w-2xl mx-4 relative z-10 ${
              production.adminNote ? "border-2 border-orange-300" : ""
            }`}
            onClick={(e) => e.stopPropagation()}
          >
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Not Detayı
                {production.adminNote && (
                  <span className="text-red-500 font-bold text-xl">!</span>
                )}
              </h3>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowAdminNoteModal(false)}
                className="h-8 w-8 p-0"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
            <div className="overflow-x-auto">
              <p className="text-base text-gray-800 whitespace-pre-wrap break-words font-medium leading-relaxed">
                {production?.adminNote || "Admin notu bulunmuyor"}
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TaskDetailPage;
