"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Search,
  Trash2,
  Save,
  X,
  Check,
  Calendar,
  Package,
  Truck,
  Archive,
  UserCheck,
  UserX,
  // RefreshCw,
  // Play,
  // Pause,
  Clock,
  AlertCircle,
  NotebookPen,
  Download,
  User2,
} from "lucide-react";
import { useState, useEffect, useCallback } from "react";
import { toast } from "sonner";
import AddVakaModal from "./_components/add-vaka-modal";
import EditHistoryModal from "./_components/edit-history-modal";
import { useAuthStore } from "@/stores/authStore";
import { useSignalR, useSignalREvent } from "@/hooks/useSignalR";
import { isMobile } from "@/lib/utils";
import { useRouter } from "next/navigation";
import { EmailCountdown } from "@/components/ui/email-countdown";
import { apiService } from "@/lib/api";
import type { Production, User } from "@/lib/types";

interface Technician {
  id: string;
  fullName?: string;
  email?: string;
  role: string;
}

// Ad ve soyad baş harflerini döndüren yardımcı fonksiyon
function getInitials(fullName?: string) {
  if (!fullName) return "";
  const [name, surname] = fullName.split(" ");
  return (name?.[0] || "").toUpperCase() + (surname?.[0] || "").toUpperCase();
}

export default function UretimPage() {
  const [productions, setProductions] = useState<Production[]>([]);
  const [technicians, setTechnicians] = useState<Technician[]>([]);
  const [users, setUsers] = useState<User[]>([]);

  const [searchTerm, setSearchTerm] = useState("");
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    total: 0,
    pStar: 0,
    greening: 0,
    approved: 0,
    stlPrint: 0,
    inProgress: 0,
    shipped: 0,
  });

  const [editingNameId, setEditingNameId] = useState<number | null>(null);
  const [editingDateId, setEditingDateId] = useState<number | null>(null);
  const [editingTargetDateId, setEditingTargetDateId] = useState<number | null>(
    null
  );
  const [editingTargetDaysId, setEditingTargetDaysId] = useState<number | null>(
    null
  );
  const [editingReplicationId, setEditingReplicationId] = useState<
    number | null
  >(null);
  const [editingNotesId, setEditingNotesId] = useState<number | null>(null);

  const [deleteConfirmId, setDeleteConfirmId] = useState<number | null>(null);
  const [archiveConfirmId, setArchiveConfirmId] = useState<number | null>(null);
  const [assignConfirmId, setAssignConfirmId] = useState<number | null>(null);

  // const [isAutoRefreshEnabled, setIsAutoRefreshEnabled] = useState(true);
  // const [countdown, setCountdown] = useState(10);
  // const [lastUpdated, setLastUpdated] = useState<Date>(new Date());

  const [nameModalData, setNameModalData] = useState({ name: "", surname: "" });
  const [notesModalData, setNotesModalData] = useState("");
  const [tempDateValue, setTempDateValue] = useState("");
  const [tempTargetDateValue, setTempTargetDateValue] = useState("");
  const [tempTargetDaysValue, setTempTargetDaysValue] = useState("");
  const [tempReplicationValue, setTempReplicationValue] = useState("");

  const { user } = useAuthStore();
  const router = useRouter();

  const [adminNoteModal, setAdminNoteModal] = useState<{
    open: boolean;
    productionId: number | null;
    note: string;
    isEdit: boolean;
  }>({ open: false, productionId: null, note: "", isEdit: false });

  const [editHistoryModal, setEditHistoryModal] = useState<{
    open: boolean;
    productionId: number | null;
    fieldName: string;
    fieldDisplayName: string;
  }>({ open: false, productionId: null, fieldName: "", fieldDisplayName: "" });

  const [exportLoading, setExportLoading] = useState(false);

  useSignalR();

  // Fetch technicians
  const fetchTechnicians = useCallback(async () => {
    try {
      const data = await apiService.getTechnicians();
      setTechnicians(data);
    } catch (error) {
      console.error("Error fetching technicians:", error);
    }
  }, []);

  const fetchUsers = useCallback(async () => {
    try {
      const data = await apiService.getAllUsers();
      setUsers(data);
    } catch (error) {
      console.error("Error fetching users:", error);
    }
  }, []);

  function getUserName(userId?: string): string {
    if (!userId) return "-";
    const u = users.find((u) => u.id === userId);

    return u?.fullName || userId;
  }

  // Task assignment functions
  const assignTask = async (productionId: number) => {
    try {
      const updatedProduction = await apiService.assignProduction(
        productionId,
        user?.id || ""
      );
      setProductions((prev) =>
        prev.map((p) => (p.id === productionId ? updatedProduction : p))
      );
      toast.success("Görev başarıyla alındı!");
    } catch (error) {
      console.error("Error assigning task:", error);
      toast.error("Görev alınamadı!");
    }
  };

  const unassignTask = async (productionId: number) => {
    try {
      const updatedProduction = await apiService.unassignProduction(
        productionId
      );
      setProductions((prev) =>
        prev.map((p) => (p.id === productionId ? updatedProduction : p))
      );
      toast.success("Görev başarıyla bırakıldı!");
    } catch (error) {
      console.error("Error unassigning task:", error);
      toast.error("Görev bırakılamadı!");
    }
  };

  const reassignTask = async (productionId: number, newUserId: string) => {
    try {
      // First unassign, then assign to new user
      await unassignTask(productionId);

      const updatedProduction = await apiService.assignProduction(
        productionId,
        newUserId
      );
      setProductions((prev) =>
        prev.map((p) => (p.id === productionId ? updatedProduction : p))
      );
      toast.success("Görev başarıyla yeniden atandı!");
    } catch (error) {
      console.error("Error reassigning task:", error);
      toast.error("Görev yeniden atanamadı!");
    }
  };

  const handleTaskAssign = (id: number) => {
    setAssignConfirmId(id);
  };

  const confirmTaskAssign = async () => {
    if (assignConfirmId && user?.id) {
      await assignTask(assignConfirmId);
      setAssignConfirmId(null);
      if (isMobile() && user?.role === "Technician") {
        router.push(`/tasks/task-detail/${assignConfirmId}`);
      }
    }
  };

  const cancelTaskAssign = () => {
    setAssignConfirmId(null);
  };

  const fetchProductions = useCallback(async () => {
    try {
      setLoading(true);
      const data = await apiService.getAllProductions();
      setProductions(data);
    } catch (error) {
      console.error("Error fetching productions:", error);
    } finally {
      setLoading(false);
    }
  }, []);

  const fetchStats = useCallback(async () => {
    try {
      const data = await apiService.getProductionStats();
      setStats(data);
    } catch (error) {
      console.error("Error fetching stats:", error);
    }
  }, []);

  // const refreshData = useCallback(async () => {
  //   try {
  //     const [productionsResponse, statsResponse] = await Promise.all([
  //       fetch(`${API_BASE_URL}/Production`),
  //       fetch(`${API_BASE_URL}/Production/stats`),
  //     ]);

  //     if (productionsResponse.ok) {
  //       const productionsData = await productionsResponse.json();
  //       setProductions(productionsData);
  //     }

  //     if (statsResponse.ok) {
  //       const statsData = await statsResponse.json();
  //       setStats(statsData);
  //     }

  //     setLastUpdated(new Date());
  //     setCountdown(10);
  //   } catch (error) {
  //     console.error("Error refreshing data:", error);
  //   }
  // }, []);

  // const formatLastUpdated = (date: Date) => {
  //   return date.toLocaleTimeString("tr-TR", {
  //     hour: "2-digit",
  //     minute: "2-digit",
  //     second: "2-digit",
  //   });
  // };

  useSignalREvent<Production>("ProductionCreated", (production) => {
    setProductions((prev) => [...prev, production]);
    fetchStats();
    toast.success(
      `Yeni üretim eklendi: ${production.name} ${production.surname}`
    );
  });

  useSignalREvent<Production>("ProductionUpdated", (production) => {
    // Only update if the production is not already updated by the current user
    setProductions((prev) => {
      const existing = prev.find((p) => p.id === production.id);
      if (existing && JSON.stringify(existing) !== JSON.stringify(production)) {
        return prev.map((p) => (p.id === production.id ? production : p));
      }
      return prev;
    });
    fetchStats();
    // toast.info(`Üretim güncellendi: ${production.name} ${production.surname}`);
  });

  useSignalREvent<number>("ProductionDeleted", (productionId) => {
    setProductions((prev) => prev.filter((p) => p.id !== productionId));
    fetchStats();
    toast.info("Üretim silindi");
  });

  const updateProduction = async (id: number, updates: Partial<Production>) => {
    try {
      const production = productions.find((p) => p.id === id);
      if (!production) return false;

      // Create a complete production object with current values and apply updates
      const updatedProduction = {
        ...production,
        ...updates,
      };

      const updatedProductionData = await apiService.updateProduction(
        id,
        updatedProduction
      );
      setProductions((prev) =>
        prev.map((p) => (p.id === id ? updatedProductionData : p))
      );
      return true;
    } catch (error) {
      console.error("Error updating production:", error);
      return false;
    }
  };

  const deleteProduction = async (id: number) => {
    try {
      await apiService.deleteProduction(id);
      setProductions((prev) => prev.filter((p) => p.id !== id));
      return true;
    } catch (error) {
      console.error("Error deleting production:", error);
    }
    return false;
  };

  useEffect(() => {
    fetchProductions();
    fetchStats();
    fetchTechnicians();
    fetchUsers();
  }, [fetchProductions, fetchStats, fetchTechnicians, fetchUsers]);

  // useEffect(() => {
  //   let intervalId: NodeJS.Timeout;

  //   if (isAutoRefreshEnabled && countdown > 0) {
  //     intervalId = setInterval(() => {
  //       setCountdown((prev) => prev - 1);
  //     }, 1000);
  //   } else if (isAutoRefreshEnabled && countdown === 0) {
  //     refreshData();
  //   }

  //   return () => {
  //     if (intervalId) clearInterval(intervalId);
  //   };
  // }, [isAutoRefreshEnabled, countdown, refreshData]);

  // useEffect(() => {
  //   if (isAutoRefreshEnabled) {
  //     setCountdown(10);
  //   }
  // }, [isAutoRefreshEnabled]);

  const calculateTargetDays = (targetDate?: string, approvalDate?: string) => {
    if (!targetDate || !approvalDate) return null;

    const target = new Date(targetDate);
    const approval = new Date(approvalDate);
    const diffTime = target.getTime() - approval.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  const calculateRemainingDays = (targetDate?: string) => {
    if (!targetDate) return null;
    const target = new Date(targetDate);
    const today = new Date();
    const diffTime = target.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return "-";
    const date = new Date(dateString);
    return date.toLocaleDateString("tr-TR", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
    });
  };

  const handleDelete = (id: number) => {
    setDeleteConfirmId(id);
  };

  const confirmDelete = async () => {
    if (deleteConfirmId) {
      const success = await deleteProduction(deleteConfirmId);
      if (success) {
        toast.success("Üretim başarıyla silindi!");
      } else {
        toast.error("Üretim silinemedi!");
      }
      setDeleteConfirmId(null);
    }
  };

  const cancelDelete = () => {
    setDeleteConfirmId(null);
  };

  const archiveProduction = async (id: number) => {
    try {
      await apiService.archiveProduction(id);
      setProductions((prev) => prev.filter((p) => p.id !== id));
      return true;
    } catch (error) {
      console.error("Error archiving production:", error);
    }
    return false;
  };

  const handleArchive = (id: number) => {
    setArchiveConfirmId(id);
  };

  const confirmArchive = async () => {
    if (archiveConfirmId) {
      const success = await archiveProduction(archiveConfirmId);
      if (success) {
        toast.success("Üretim başarıyla arşivlendi!");
      } else {
        toast.error("Üretim arşivlenemedi!");
      }
      setArchiveConfirmId(null);
    }
  };

  const cancelArchive = () => {
    setArchiveConfirmId(null);
  };

  const handleVakaAdded = () => {
    fetchProductions();
    fetchStats();
    toast.success("Yeni vaka başarıyla eklendi!");
  };

  const handleExportToExcel = async () => {
    try {
      setExportLoading(true);
      const blob = await apiService.exportActiveExcel();

      const url = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;

      // Default dosya adı
      const fileName = `${new Date()
        .toISOString()
        .slice(0, 10)
        .replace(/-/g, "")}_Aktif_Üretimler.xlsx`;

      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      toast.success("Excel raporu başarıyla indirildi!");
    } catch (error) {
      console.error("Excel export error:", error);
      toast.error("Excel raporu indirilemedi!");
    } finally {
      setExportLoading(false);
    }
  };

  const handleCheckboxChange = async (
    id: number,
    field: keyof Production,
    value: boolean
  ) => {
    try {
      // Optimistic update
      setProductions((prev) =>
        prev.map((p) => (p.id === id ? { ...p, [field]: value } : p))
      );

      const success = await updateProduction(id, { [field]: value });
      if (success) {
        toast.success("Değişiklik başarıyla kaydedildi!");
      } else {
        // Revert optimistic update on failure
        setProductions((prev) =>
          prev.map((p) => (p.id === id ? { ...p, [field]: !value } : p))
        );
        toast.error("Değişiklik kaydedilemedi!");
      }
    } catch {
      // Revert optimistic update on error
      setProductions((prev) =>
        prev.map((p) => (p.id === id ? { ...p, [field]: !value } : p))
      );
      toast.error("Değişiklik kaydedilemedi!");
    }
  };

  const handleNameEdit = (id: number, name: string, surname: string) => {
    setEditingNameId(id);
    setNameModalData({ name, surname });
  };

  const handleNameSave = async () => {
    if (editingNameId) {
      const success = await updateProduction(editingNameId, nameModalData);
      if (success) {
        toast.success("Ad soyad başarıyla güncellendi!");
        setEditingNameId(null);
        setNameModalData({ name: "", surname: "" });
      } else {
        toast.error("Ad soyad güncellenemedi!");
      }
    }
  };

  const handleNameCancel = () => {
    setEditingNameId(null);
    setNameModalData({ name: "", surname: "" });
  };

  const handleDateEdit = (id: number, currentDate: string) => {
    setEditingDateId(id);
    setTempDateValue(currentDate || "");
  };

  const handleDateSave = async () => {
    if (editingDateId) {
      const success = await updateProduction(editingDateId, {
        approvalDate: tempDateValue,
      });
      if (success) {
        toast.success("Onay tarihi başarıyla güncellendi!");
        setEditingDateId(null);
        setTempDateValue("");
      } else {
        toast.error("Onay tarihi güncellenemedi!");
      }
    }
  };

  const handleDateCancel = () => {
    setEditingDateId(null);
    setTempDateValue("");
  };

  const handleTargetDateEdit = (id: number, currentDate: string) => {
    setEditingTargetDateId(id);
    setTempTargetDateValue(currentDate || "");
  };

  const handleTargetDateSave = async () => {
    if (editingTargetDateId) {
      const success = await updateProduction(editingTargetDateId, {
        targetDate: tempTargetDateValue,
      });
      if (success) {
        toast.success("Teslimat tarihi başarıyla güncellendi!");
        setEditingTargetDateId(null);
        setTempTargetDateValue("");
      } else {
        toast.error("Teslimat tarihi güncellenemedi!");
      }
    }
  };

  const handleTargetDateCancel = () => {
    setEditingTargetDateId(null);
    setTempTargetDateValue("");
  };

  const handleTargetDaysEdit = (id: number, currentDays: number) => {
    setEditingTargetDaysId(id);
    setTempTargetDaysValue(currentDays.toString());
  };

  const handleTargetDaysSave = async () => {
    if (editingTargetDaysId) {
      const days = parseInt(tempTargetDaysValue) || 0;
      const production = productions.find((p) => p.id === editingTargetDaysId);

      if (production) {
        const startDate = production.approvalDate
          ? new Date(production.approvalDate)
          : new Date();

        const targetDate = new Date(startDate);
        targetDate.setDate(targetDate.getDate() + days);

        const targetDateString = targetDate.toISOString().split("T")[0];

        const success = await updateProduction(editingTargetDaysId, {
          targetDate: targetDateString,
          ...(production.approvalDate
            ? {}
            : { approvalDate: new Date().toISOString().split("T")[0] }),
        });

        if (success) {
          toast.success("Hedef gün başarıyla güncellendi!");
          setEditingTargetDaysId(null);
          setTempTargetDaysValue("");
        } else {
          toast.error("Hedef gün güncellenemedi!");
        }
      } else {
        toast.error("Üretim bulunamadı!");
      }
    }
  };

  const handleTargetDaysCancel = () => {
    setEditingTargetDaysId(null);
    setTempTargetDaysValue("");
  };

  const handleReplicationEdit = (id: number, currentValue: number) => {
    setEditingReplicationId(id);
    setTempReplicationValue(currentValue.toString());
  };

  const handleReplicationSave = async () => {
    if (editingReplicationId) {
      const success = await updateProduction(editingReplicationId, {
        replication: parseInt(tempReplicationValue) || 0,
      });
      if (success) {
        toast.success("Çoğaltma sayısı başarıyla güncellendi!");
        setEditingReplicationId(null);
        setTempReplicationValue("");
      } else {
        toast.error("Çoğaltma sayısı güncellenemedi!");
      }
    }
  };

  const handleReplicationCancel = () => {
    setEditingReplicationId(null);
    setTempReplicationValue("");
  };

  const handleNotesEdit = (id: number, currentNotes: string) => {
    setEditingNotesId(id);
    setNotesModalData(currentNotes || "");
  };

  const handleNotesSave = async () => {
    if (editingNotesId) {
      const success = await updateProduction(editingNotesId, {
        notes: notesModalData,
      });
      if (success) {
        toast.success("Açıklama başarıyla güncellendi!");
        setEditingNotesId(null);
        setNotesModalData("");
      } else {
        toast.error("Açıklama güncellenemedi!");
      }
    }
  };

  const handleNotesCancel = () => {
    setEditingNotesId(null);
    setNotesModalData("");
  };

  const filteredProductions = productions
    .filter(
      (p) =>
        p.name
          .toLocaleLowerCase("tr")
          .includes(searchTerm.toLocaleLowerCase("tr")) ||
        p.surname
          .toLocaleLowerCase("tr")
          .includes(searchTerm.toLocaleLowerCase("tr")) ||
        (p.notes &&
          p.notes
            .toLocaleLowerCase("tr")
            .includes(searchTerm.toLocaleLowerCase("tr")))
    )
    .sort((a, b) => {
      const remainingA = calculateRemainingDays(a.targetDate);
      const remainingB = calculateRemainingDays(b.targetDate);

      if (remainingA === null && remainingB === null) return 0;
      if (remainingA === null) return 1;
      if (remainingB === null) return -1;

      if (remainingA !== remainingB) {
        return remainingA - remainingB;
      }
      const nameA = (a.name + " " + a.surname).toLowerCase();
      const nameB = (b.name + " " + b.surname).toLowerCase();
      if (nameA < nameB) return -1;
      if (nameA > nameB) return 1;
      return 0;
    });

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg">Yükleniyor...</div>
      </div>
    );
  }

  // const toggleAutoRefresh = () => {
  //   setIsAutoRefreshEnabled((prev) => {
  //     if (!prev) setCountdown(10);
  //     return !prev;
  //   });
  // };

  const handleAdminNoteView = (id: number, note: string) => {
    setAdminNoteModal({ open: true, productionId: id, note, isEdit: false });
  };
  const handleAdminNoteEdit = (id: number, note: string) => {
    setAdminNoteModal({ open: true, productionId: id, note, isEdit: true });
  };
  const handleAdminNoteSave = async () => {
    if (adminNoteModal.productionId !== null) {
      const success = await updateProduction(adminNoteModal.productionId, {
        adminNote: adminNoteModal.note,
      });
      if (success) {
        toast.success("Not başarıyla kaydedildi!");
        setAdminNoteModal({
          open: false,
          productionId: null,
          note: "",
          isEdit: false,
        });
      } else {
        toast.error("Not kaydedilemedi!");
      }
    }
  };

  const handleAdminNoteDelete = async () => {
    if (adminNoteModal.productionId !== null) {
      const success = await updateProduction(adminNoteModal.productionId, {
        adminNote: "",
      });
      if (success) {
        toast.success("Not silindi!");
        setAdminNoteModal({
          open: false,
          productionId: null,
          note: "",
          isEdit: false,
        });
      } else {
        toast.error("Not silinemedi!");
      }
    }
  };

  const handleEditHistoryOpen = (
    productionId: number,
    fieldName: string,
    fieldDisplayName: string
  ) => {
    setEditHistoryModal({
      open: true,
      productionId,
      fieldName,
      fieldDisplayName,
    });
  };

  const handleEditHistoryClose = () => {
    setEditHistoryModal({
      open: false,
      productionId: null,
      fieldName: "",
      fieldDisplayName: "",
    });
  };

  return (
    <TooltipProvider>
      <div className="space-y-6 w-full mx-auto">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="sm:text-3xl text-2xl font-bold text-gray-900">
              Üretim Yönetimi
            </h1>
            <p className="text-gray-600 mt-2 hidden sm:block">
              Üretim süreçlerini takip edin ve yönetin
            </p>
          </div>
          {user?.role === "Admin" && (
            <div className="flex items-center gap-4">
              <EmailCountdown
                onManualSend={() =>
                  toast.success("Email raporları gönderildi!")
                }
              />
              <Button
                className="flex items-center gap-2"
                onClick={handleExportToExcel}
                disabled={exportLoading}
                variant="outline"
              >
                {exportLoading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-2 border-gray-600 border-t-transparent"></div>
                    İndiriliyor...
                  </>
                ) : (
                  <>
                    <Download className="h-4 w-4" />
                    Rapor İndir
                  </>
                )}
              </Button>
              <AddVakaModal onSuccess={handleVakaAdded} />
            </div>
          )}
        </div>

        {/* Stats */}
        <div className="grid grid-cols-2 sm:grid-cols-4 md:grid-cols-7 gap-4 sm:gap-6">
          <Card>
            <CardContent className="p-4 sm:p-6">
              <div className="flex items-center justify-between ">
                <div>
                  <p className="text-xs sm:text-sm font-medium text-gray-600">
                    Toplam
                  </p>
                </div>
                <Package className="h-6 w-6 sm:h-6 sm:w-6 text-blue-600" />
              </div>
              <p className="text-lg sm:text-2xl font-bold text-gray-900">
                {stats.total}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4 sm:p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-xs sm:text-sm font-medium text-gray-600">
                    P Yıldız
                  </p>
                </div>
                <div className="h-6 w-6 sm:h-7 sm:w-7 bg-blue-100 rounded-full flex items-center justify-center">
                  <span className="text-blue-600 font-bold text-xs sm:text-xs">
                    P*
                  </span>
                </div>
              </div>
              <p className="text-lg sm:text-2xl font-bold text-gray-900">
                {stats.pStar}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4 sm:p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-xs sm:text-sm font-medium text-gray-600">
                    Yeşil
                  </p>
                </div>
                <div className="h-6 w-6 sm:h-7 sm:w-7 bg-green-100 rounded-full flex items-center justify-center">
                  <span className="text-green-600 font-bold text-xs sm:text-xs">
                    Y
                  </span>
                </div>
              </div>
              <p className="text-lg sm:text-2xl font-bold text-gray-900">
                {stats.greening}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4 sm:p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-xs sm:text-sm font-medium text-gray-600">
                    Onay
                  </p>
                </div>
                <Check className="h-6 w-6 sm:h-7 sm:w-7 text-green-600" />
              </div>
              <p className="text-lg sm:text-2xl font-bold text-gray-900">
                {stats.approved}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4 sm:p-6">
              <div className="flex items-center justify-between ">
                <div>
                  <p className="text-xs sm:text-sm font-medium text-gray-600">
                    STL Ürt.
                  </p>
                </div>
                <div className="h-6 w-6 sm:h-7 sm:w-7 bg-yellow-100 rounded-full flex items-center justify-center">
                  <span className="text-yellow-600 font-bold text-xs sm:text-xs">
                    STL
                  </span>
                </div>
              </div>
              <p className="text-lg sm:text-2xl font-bold text-gray-900">
                {stats.stlPrint}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4 sm:p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-xs sm:text-sm font-medium text-gray-600">
                    Devam Ed.
                  </p>
                </div>
                <Calendar className="h-6 w-6 sm:h-7 sm:w-7 text-orange-600" />
              </div>
              <p className="text-lg sm:text-2xl font-bold text-gray-900">
                {stats.inProgress}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4 sm:p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-xs sm:text-sm font-medium text-gray-600">
                    Gönderilen
                  </p>
                </div>
                <Truck className="h-6 w-6 sm:h-7 sm:w-7 text-purple-600" />
              </div>
              <p className="text-lg sm:text-2xl font-bold text-gray-900">
                {stats.shipped}
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Search */}
        <Card>
          <CardContent className="p-6">
            <div className="flex gap-4">
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  type="text"
                  placeholder="Hasta adı, soyadı veya notlarda ara..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Production Table */}
        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <CardTitle className="sm:text-xl text-lg">
                Üretim Listesi
              </CardTitle>

              {/* Auto Refresh Widget - Moved to table header */}
              {/* <div>
                <Card className="border-0 shadow-sm px-1">
                  <CardContent className="sm:p-3 p-1">
                    <div className="flex items-center gap-3">
                      <div className="flex items-center gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={toggleAutoRefresh}
                          className="sm:h-8 h-7 sm:w-8 w-7 p-0"
                        >
                          {isAutoRefreshEnabled ? (
                            <Pause className="h-4 w-4" />
                          ) : (
                            <Play className="h-4 w-4" />
                          )}
                        </Button>

                        <Button
                          variant="outline"
                          size="sm"
                          onClick={refreshData}
                          className="sm:h-8 h-7 sm:w-8 w-7 p-0"
                          disabled={loading}
                        >
                          <RefreshCw
                            className={`h-4 w-4 ${
                              loading ? "animate-spin" : ""
                            }`}
                          />
                        </Button>
                      </div>

                      <div className="flex flex-col items-center ">
                        <div className="flex items-center gap-1 text-xs text-gray-600">
                          <Clock className="h-3 w-3" />
                          {isAutoRefreshEnabled ? (
                            <span>Yenileme: {countdown}s</span>
                          ) : (
                            <span>Duraklatıldı</span>
                          )}
                        </div>
                        <div className="text-xs text-gray-500">
                          Son: {formatLastUpdated(lastUpdated)}
                        </div>
                      </div>

                      {isAutoRefreshEnabled && (
                        <div className="w-16 h-1 sm:block hidden  bg-gray-200 rounded-full overflow-hidden">
                          <div
                            className="h-full bg-blue-500 transition-all duration-1000 ease-linear"
                            style={{
                              width: `${((10 - countdown) / 10) * 100}%`,
                            }}
                          />
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
                {isAutoRefreshEnabled && (
                  <div className="w-full h-1 mt-1 sm:hidden   bg-gray-200 rounded-full overflow-hidden">
                    <div
                      className="h-full bg-blue-500 transition-all duration-1000 ease-linear"
                      style={{ width: `${((10 - countdown) / 10) * 100}%` }}
                    />
                  </div>
                )}
              </div> */}
            </div>
          </CardHeader>
          <CardContent className="p-0">
            <div className="overflow-x-auto max-w-full">
              <div className="px-6 py-4">
                <table className="w-full ">
                  <thead>
                    <tr className="border-b">
                      {/* sıra no */}
                      <th className="hidden sm:table-cell text-center text-sm py-3 px-3 font-medium text-gray-900 !w-[1px] !max-w-[1px] "></th>
                      <th
                        className={`text-left sm:text-sm text-xs py-3 px-1  font-medium text-gray-900 `}
                      >
                        Ad Soyad
                      </th>
                      <th
                        className={`hidden sm:table-cell text-center text-sm py-3 px-2 font-medium text-gray-900 `}
                      >
                        Not
                      </th>
                      <th
                        className={`hidden sm:table-cell text-center text-sm py-3 px-2 font-medium text-gray-900 `}
                      >
                        Onay Kutuları
                      </th>
                      <th
                        className={`hidden sm:table-cell text-left text-sm py-3 px-2 font-medium text-gray-900 `}
                      >
                        Onay Tarihi
                      </th>
                      <th
                        className={`hidden sm:table-cell text-left text-sm py-3 px-2 font-medium text-gray-900 `}
                      >
                        Hedef Gün
                      </th>
                      <th
                        className={`hidden sm:table-cell text-left text-sm py-3 px-2 font-medium text-gray-900 `}
                      >
                        Teslimat Tarihi
                      </th>
                      <th
                        className={`text-left sm:text-sm text-xs py-3 px-2 font-medium text-gray-900 `}
                      >
                        Kalan Gün
                      </th>
                      <th
                        className={`hidden sm:table-cell text-center text-sm py-3 px-2 font-medium text-gray-900  `}
                      >
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <span className="cursor-pointer">Ç</span>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Çoğaltma</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </th>
                      <th
                        className={`hidden sm:table-cell text-center text-sm py-3 px-2 font-medium text-gray-900  `}
                      >
                        Süreç Takibi
                      </th>
                      <th
                        className={`text-center sm:text-sm text-xs py-3 px-2 font-medium text-gray-900 `}
                      >
                        Teknisyen Görev
                      </th>
                      <th
                        className={`hidden sm:table-cell text-left text-sm py-3 px-2 font-medium text-gray-900  `}
                      >
                        Açıklama
                      </th>
                      {user?.role === "Admin" && (
                        <th className="hidden sm:table-cell text-center text-sm py-3 px-2 font-medium text-gray-900  ">
                          İşlemler
                        </th>
                      )}
                    </tr>
                  </thead>
                  <tbody>
                    {filteredProductions.map((production, index) => {
                      const remainingDays = calculateRemainingDays(
                        production.targetDate
                      );
                      const targetDaysCount = calculateTargetDays(
                        production.targetDate,
                        production.approvalDate
                      );

                      return (
                        <tr
                          key={production.id}
                          className={`border-b hover:bg-gray-50 ${
                            remainingDays !== null
                              ? remainingDays < 0
                                ? "border-l-4 border-l-red-700 bg-red-50"
                                : remainingDays <= 2 && remainingDays >= 0
                                ? "animate-pulse border-l-4 border-l-red-500 bg-red-50"
                                : ""
                              : ""
                          }`}
                        >
                          {/* Sıra No */}
                          <td className="hidden sm:table-cell py-3 px-1 text-center border   ">
                            <span className="font-medium text-gray-700 !text-[13px] ">
                              {index + 1}
                            </span>
                          </td>

                          {/* Ad Soyad */}
                          <td className={`py-3 px-1   `}>
                            <div
                              className={`flex items-center gap-2 ${
                                user?.role === "Admin" ? "cursor-pointer" : ""
                              }`}
                              onClick={
                                user?.role === "Admin"
                                  ? () =>
                                      handleNameEdit(
                                        production.id,
                                        production.name,
                                        production.surname
                                      )
                                  : undefined
                              }
                            >
                              <TooltipProvider>
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <span className="sm:text-sm text-xs font-medium text-gray-900  block truncate cursor-help">
                                      {(() => {
                                        const fullName = `${production.name} ${production.surname}`;
                                        return fullName.length > 15
                                          ? `${fullName.substring(0, 15)}...`
                                          : fullName;
                                      })()}
                                    </span>
                                  </TooltipTrigger>
                                  <TooltipContent className="whitespace-pre-line break-words min-w-[200px] max-w-[400px]">
                                    <div>
                                      <p className="font-medium">
                                        {production.name} {production.surname}
                                      </p>
                                      {production.notes && (
                                        <p className="mt-1 text-sm whitespace-pre-line break-words">
                                          {production.notes}
                                        </p>
                                      )}
                                      {user?.role === "Admin" &&
                                        production.nameLastEditedBy && (
                                          <div className="mt-3 pt-2 border-t border-gray-200">
                                            <button
                                              onClick={() =>
                                                handleEditHistoryOpen(
                                                  production.id,
                                                  "Name",
                                                  "Ad Soyad"
                                                )
                                              }
                                              className="flex items-start gap-2 text-xs hover:bg-gray-100 p-1 rounded transition-colors w-full text-left"
                                            >
                                              <User2 className="h-3 w-3 text-gray-400 mt-0.5 flex-shrink-0" />
                                              <div className="flex-1 min-w-0">
                                                <div className="flex items-center gap-1 mb-1">
                                                  <span className="text-gray-400 font-medium">
                                                    Son düzenleyen:
                                                  </span>
                                                  <span className="text-gray-200 font-semibold">
                                                    {getUserName(
                                                      production.nameLastEditedBy
                                                    )}
                                                  </span>
                                                </div>
                                                {production.nameLastEditedAt && (
                                                  <div className="flex items-center gap-1 text-gray-400">
                                                    <Clock className="h-3 w-3" />
                                                    <span>
                                                      {new Date(
                                                        production.nameLastEditedAt
                                                      ).toLocaleDateString(
                                                        "tr-TR",
                                                        {
                                                          day: "2-digit",
                                                          month: "2-digit",
                                                          year: "numeric",
                                                        }
                                                      )}
                                                    </span>
                                                    <span className="text-gray-400">
                                                      •
                                                    </span>
                                                    <span>
                                                      {new Date(
                                                        production.nameLastEditedAt
                                                      ).toLocaleTimeString(
                                                        "tr-TR",
                                                        {
                                                          hour: "2-digit",
                                                          minute: "2-digit",
                                                        }
                                                      )}
                                                    </span>
                                                  </div>
                                                )}
                                              </div>
                                            </button>
                                          </div>
                                        )}
                                    </div>
                                  </TooltipContent>
                                </Tooltip>
                              </TooltipProvider>
                            </div>
                          </td>

                          {/* Not */}
                          <td
                            className={`py-3 px-2 text-center hidden sm:table-cell  `}
                          >
                            {production.adminNote ? (
                              <TooltipProvider>
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <span
                                      className="cursor-pointer"
                                      onClick={() =>
                                        handleAdminNoteView(
                                          production.id,
                                          production.adminNote!
                                        )
                                      }
                                    >
                                      <AlertCircle className="h-5 w-5 text-yellow-500" />
                                    </span>
                                  </TooltipTrigger>
                                  <TooltipContent>
                                    <p>Not var</p>
                                  </TooltipContent>
                                </Tooltip>
                              </TooltipProvider>
                            ) : null}
                          </td>

                          {/* Onay Kutuları */}
                          <td className={`py-3 px-2 hidden sm:table-cell  `}>
                            <div className="flex gap-3 justify-center  ">
                              <TooltipProvider>
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <div className="flex items-center ">
                                      <input
                                        type="checkbox"
                                        checked={production.pStar}
                                        onChange={(e) =>
                                          handleCheckboxChange(
                                            production.id,
                                            "pStar",
                                            e.target.checked
                                          )
                                        }
                                        className="w-4 h-4 text-blue-600"
                                        disabled={
                                          user?.role !== "Admin" &&
                                          user?.role !== "Software"
                                        }
                                      />
                                    </div>
                                  </TooltipTrigger>
                                  <TooltipContent className="max-w-sm">
                                    <div>
                                      <p className="font-medium mb-1">P*</p>
                                      {user?.role === "Admin" &&
                                        production.pStarLastEditedBy && (
                                          <div className="mt-3 pt-2 border-t border-gray-200">
                                            <button
                                              onClick={() =>
                                                handleEditHistoryOpen(
                                                  production.id,
                                                  "PStar",
                                                  "P*"
                                                )
                                              }
                                              className="flex items-start gap-2 text-xs cursor-pointer p-1 rounded transition-colors w-full text-left"
                                            >
                                              <User2 className="h-3 w-3 text-gray-400 mt-0.5 flex-shrink-0" />
                                              <div className="flex-1 min-w-0">
                                                <div className="flex items-center gap-1 mb-1">
                                                  <span className="text-gray-400 font-medium">
                                                    Son düzenleyen:
                                                  </span>
                                                  <span className="text-gray-200 font-semibold">
                                                    {getUserName(
                                                      production.pStarLastEditedBy
                                                    )}
                                                  </span>
                                                </div>
                                                {production.pStarLastEditedAt && (
                                                  <div className="flex items-center gap-1 text-gray-400">
                                                    <Clock className="h-3 w-3" />
                                                    <span>
                                                      {new Date(
                                                        production.pStarLastEditedAt
                                                      ).toLocaleDateString(
                                                        "tr-TR",
                                                        {
                                                          day: "2-digit",
                                                          month: "2-digit",
                                                          year: "numeric",
                                                        }
                                                      )}
                                                    </span>
                                                    <span className="text-gray-400">
                                                      •
                                                    </span>
                                                    <span>
                                                      {new Date(
                                                        production.pStarLastEditedAt
                                                      ).toLocaleTimeString(
                                                        "tr-TR",
                                                        {
                                                          hour: "2-digit",
                                                          minute: "2-digit",
                                                        }
                                                      )}
                                                    </span>
                                                  </div>
                                                )}
                                              </div>
                                            </button>
                                          </div>
                                        )}
                                    </div>
                                  </TooltipContent>
                                </Tooltip>
                              </TooltipProvider>
                              <TooltipProvider>
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <div className="flex items-center">
                                      <input
                                        type="checkbox"
                                        checked={production.greening}
                                        onChange={(e) =>
                                          handleCheckboxChange(
                                            production.id,
                                            "greening",
                                            e.target.checked
                                          )
                                        }
                                        className="w-4 h-4 text-green-600"
                                        disabled={
                                          user?.role !== "Admin" &&
                                          user?.role !== "Software"
                                        }
                                      />
                                    </div>
                                  </TooltipTrigger>
                                  <TooltipContent className="max-w-sm">
                                    <div>
                                      <p className="font-medium mb-1">Yeşil</p>
                                      {user?.role === "Admin" &&
                                        production.greeningLastEditedBy && (
                                          <div className="mt-3 pt-2 border-t border-gray-200">
                                            <button
                                              onClick={() =>
                                                handleEditHistoryOpen(
                                                  production.id,
                                                  "Greening",
                                                  "Yeşil"
                                                )
                                              }
                                              className="flex items-start gap-2 text-xs cursor-pointer p-1 rounded transition-colors w-full text-left"
                                            >
                                              <User2 className="h-3 w-3 text-gray-400 mt-0.5 flex-shrink-0" />
                                              <div className="flex-1 min-w-0">
                                                <div className="flex items-center gap-1 mb-1">
                                                  <span className="text-gray-400 font-medium">
                                                    Son düzenleyen:
                                                  </span>
                                                  <span className="text-gray-200 font-semibold">
                                                    {getUserName(
                                                      production.greeningLastEditedBy
                                                    )}
                                                  </span>
                                                </div>
                                                {production.greeningLastEditedAt && (
                                                  <div className="flex items-center gap-1 text-gray-400">
                                                    <Clock className="h-3 w-3" />
                                                    <span>
                                                      {new Date(
                                                        production.greeningLastEditedAt
                                                      ).toLocaleDateString(
                                                        "tr-TR",
                                                        {
                                                          day: "2-digit",
                                                          month: "2-digit",
                                                          year: "numeric",
                                                        }
                                                      )}
                                                    </span>
                                                    <span className="text-gray-400">
                                                      •
                                                    </span>
                                                    <span>
                                                      {new Date(
                                                        production.greeningLastEditedAt
                                                      ).toLocaleTimeString(
                                                        "tr-TR",
                                                        {
                                                          hour: "2-digit",
                                                          minute: "2-digit",
                                                        }
                                                      )}
                                                    </span>
                                                  </div>
                                                )}
                                              </div>
                                            </button>
                                          </div>
                                        )}
                                    </div>
                                  </TooltipContent>
                                </Tooltip>
                              </TooltipProvider>
                              <TooltipProvider>
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <div className="flex items-center">
                                      <input
                                        type="checkbox"
                                        checked={production.adminApproval}
                                        onChange={(e) =>
                                          handleCheckboxChange(
                                            production.id,
                                            "adminApproval",
                                            e.target.checked
                                          )
                                        }
                                        className="w-4 h-4 text-purple-600"
                                        disabled={
                                          user?.role !== "Admin" &&
                                          user?.role !== "Software"
                                        }
                                      />
                                    </div>
                                  </TooltipTrigger>
                                  <TooltipContent className="max-w-sm">
                                    <div>
                                      <p className="font-medium mb-1">Onay</p>
                                      {user?.role === "Admin" &&
                                        production.adminApprovalLastEditedBy && (
                                          <div className="mt-3 pt-2 border-t border-gray-200">
                                            <button
                                              onClick={() =>
                                                handleEditHistoryOpen(
                                                  production.id,
                                                  "AdminApproval",
                                                  "Onay"
                                                )
                                              }
                                              className="flex items-start gap-2 text-xs cursor-pointer p-1 rounded transition-colors w-full text-left"
                                            >
                                              <User2 className="h-3 w-3 text-gray-400 mt-0.5 flex-shrink-0" />
                                              <div className="flex-1 min-w-0">
                                                <div className="flex items-center gap-1 mb-1">
                                                  <span className="text-gray-400 font-medium">
                                                    Son düzenleyen:
                                                  </span>
                                                  <span className="text-gray-200 font-semibold">
                                                    {getUserName(
                                                      production.adminApprovalLastEditedBy
                                                    )}
                                                  </span>
                                                </div>
                                                {production.adminApprovalLastEditedAt && (
                                                  <div className="flex items-center gap-1 text-gray-400">
                                                    <Clock className="h-3 w-3" />
                                                    <span>
                                                      {new Date(
                                                        production.adminApprovalLastEditedAt
                                                      ).toLocaleDateString(
                                                        "tr-TR",
                                                        {
                                                          day: "2-digit",
                                                          month: "2-digit",
                                                          year: "numeric",
                                                        }
                                                      )}
                                                    </span>
                                                    <span className="text-gray-400">
                                                      •
                                                    </span>
                                                    <span>
                                                      {new Date(
                                                        production.adminApprovalLastEditedAt
                                                      ).toLocaleTimeString(
                                                        "tr-TR",
                                                        {
                                                          hour: "2-digit",
                                                          minute: "2-digit",
                                                        }
                                                      )}
                                                    </span>
                                                  </div>
                                                )}
                                              </div>
                                            </button>
                                          </div>
                                        )}
                                    </div>
                                  </TooltipContent>
                                </Tooltip>
                              </TooltipProvider>
                              <TooltipProvider>
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <div className="flex items-center">
                                      <div className="relative">
                                        <input
                                          type="checkbox"
                                          checked={production.stlPrint}
                                          className="sr-only"
                                          disabled={
                                            user?.role !== "Admin" &&
                                            user?.role !== "Software"
                                          }
                                        />
                                        <div
                                          className={`
                                          w-6 h-6 rounded border-2 border-yellow-400 cursor-pointer transition-all duration-200 flex items-center justify-center
                                          ${
                                            production.stlPrint
                                              ? "bg-yellow-400 border-yellow-500 shadow-sm"
                                              : "bg-white border-gray-300 hover:border-yellow-400"
                                          }
                                          ${
                                            user?.role !== "Admin" &&
                                            user?.role !== "Software"
                                              ? "opacity-50 cursor-not-allowed"
                                              : "hover:shadow-md"
                                          }
                                        `}
                                          onClick={(e) => {
                                            e.preventDefault();
                                            e.stopPropagation();
                                            if (
                                              user?.role === "Admin" ||
                                              user?.role === "Software"
                                            ) {
                                              handleCheckboxChange(
                                                production.id,
                                                "stlPrint",
                                                !production.stlPrint
                                              );
                                            }
                                          }}
                                        >
                                          {production.stlPrint && (
                                            <svg
                                              className="w-3 h-3 text-black"
                                              fill="currentColor"
                                              viewBox="0 0 20 20"
                                            >
                                              <path
                                                fillRule="evenodd"
                                                d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                                clipRule="evenodd"
                                              />
                                            </svg>
                                          )}
                                        </div>
                                      </div>
                                    </div>
                                  </TooltipTrigger>
                                  <TooltipContent className="max-w-sm">
                                    <div>
                                      <p className="font-medium mb-1">
                                        STL Üretimi
                                      </p>
                                      {user?.role === "Admin" &&
                                        production.stlPrintLastEditedBy && (
                                          <div className="mt-3 pt-2 border-t border-gray-200">
                                            <button
                                              onClick={() =>
                                                handleEditHistoryOpen(
                                                  production.id,
                                                  "StlPrint",
                                                  "STL Üretimi"
                                                )
                                              }
                                              className="flex items-start gap-2 text-xs cursor-pointer p-1 rounded transition-colors w-full text-left"
                                            >
                                              <User2 className="h-3 w-3 text-gray-400 mt-0.5 flex-shrink-0" />
                                              <div className="flex-1 min-w-0">
                                                <div className="flex items-center gap-1 mb-1">
                                                  <span className="text-gray-400 font-medium">
                                                    Son düzenleyen:
                                                  </span>
                                                  <span className="text-gray-200 font-semibold">
                                                    {getUserName(
                                                      production.stlPrintLastEditedBy
                                                    )}
                                                  </span>
                                                </div>
                                                {production.stlPrintLastEditedAt && (
                                                  <div className="flex items-center gap-1 text-gray-400">
                                                    <Clock className="h-3 w-3" />
                                                    <span>
                                                      {new Date(
                                                        production.stlPrintLastEditedAt
                                                      ).toLocaleDateString(
                                                        "tr-TR",
                                                        {
                                                          day: "2-digit",
                                                          month: "2-digit",
                                                          year: "numeric",
                                                        }
                                                      )}
                                                    </span>
                                                    <span className="text-gray-400">
                                                      •
                                                    </span>
                                                    <span>
                                                      {new Date(
                                                        production.stlPrintLastEditedAt
                                                      ).toLocaleTimeString(
                                                        "tr-TR",
                                                        {
                                                          hour: "2-digit",
                                                          minute: "2-digit",
                                                        }
                                                      )}
                                                    </span>
                                                  </div>
                                                )}
                                              </div>
                                            </button>
                                          </div>
                                        )}
                                    </div>
                                  </TooltipContent>
                                </Tooltip>
                              </TooltipProvider>
                            </div>
                          </td>

                          {/* Onay Tarihi */}
                          <td className={`py-3 px-2 hidden sm:table-cell   `}>
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <div
                                    className={`flex items-center gap-2 relative ${
                                      user?.role === "Admin"
                                        ? "cursor-pointer"
                                        : ""
                                    }`}
                                    onClick={
                                      user?.role === "Admin"
                                        ? () =>
                                            handleDateEdit(
                                              production.id,
                                              production.approvalDate || ""
                                            )
                                        : undefined
                                    }
                                  >
                                    <span className="text-sm cursor-help">
                                      {formatDate(production.approvalDate)}
                                    </span>
                                  </div>
                                </TooltipTrigger>
                                <TooltipContent className="max-w-sm">
                                  <div>
                                    <p className="font-medium mb-1">
                                      Onay Tarihi
                                    </p>
                                    <p className="text-sm mb-2">
                                      {formatDate(production.approvalDate)}
                                    </p>
                                    {user?.role === "Admin" &&
                                      production.approvalDateLastEditedBy && (
                                        <div className="mt-3 pt-2 border-t border-gray-200">
                                          <button
                                            onClick={() =>
                                              handleEditHistoryOpen(
                                                production.id,
                                                "ApprovalDate",
                                                "Onay Tarihi"
                                              )
                                            }
                                            className="flex items-start gap-2 text-xs cursor-pointer p-1 rounded transition-colors w-full text-left"
                                          >
                                            <User2 className="h-3 w-3 text-gray-400 mt-0.5 flex-shrink-0" />
                                            <div className="flex-1 min-w-0">
                                              <div className="flex items-center gap-1 mb-1">
                                                <span className="text-gray-400 font-medium">
                                                  Son düzenleyen:
                                                </span>
                                                <span className="text-gray-200 font-semibold">
                                                  {getUserName(
                                                    production.approvalDateLastEditedBy
                                                  )}
                                                </span>
                                              </div>
                                              {production.approvalDateLastEditedAt && (
                                                <div className="flex items-center gap-1 text-gray-400">
                                                  <Clock className="h-3 w-3" />
                                                  <span>
                                                    {new Date(
                                                      production.approvalDateLastEditedAt
                                                    ).toLocaleDateString(
                                                      "tr-TR",
                                                      {
                                                        day: "2-digit",
                                                        month: "2-digit",
                                                        year: "numeric",
                                                      }
                                                    )}
                                                  </span>
                                                  <span className="text-gray-400">
                                                    •
                                                  </span>
                                                  <span>
                                                    {new Date(
                                                      production.approvalDateLastEditedAt
                                                    ).toLocaleTimeString(
                                                      "tr-TR",
                                                      {
                                                        hour: "2-digit",
                                                        minute: "2-digit",
                                                      }
                                                    )}
                                                  </span>
                                                </div>
                                              )}
                                            </div>
                                          </button>
                                        </div>
                                      )}
                                  </div>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                          </td>

                          {/* Hedef Gün */}
                          <td className={`py-3 px-2 hidden sm:table-cell `}>
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <div
                                    className={`flex items-center gap-2 ${
                                      user?.role === "Admin"
                                        ? "cursor-pointer"
                                        : ""
                                    }`}
                                    onClick={
                                      user?.role === "Admin"
                                        ? () =>
                                            handleTargetDaysEdit(
                                              production.id,
                                              targetDaysCount || 0
                                            )
                                        : undefined
                                    }
                                  >
                                    <span className="text-sm cursor-help">
                                      {targetDaysCount !== null
                                        ? `${targetDaysCount} gün`
                                        : "-"}
                                    </span>
                                  </div>
                                </TooltipTrigger>
                                <TooltipContent className="max-w-sm">
                                  <div>
                                    <p className="font-medium mb-1">
                                      Hedef Gün
                                    </p>
                                    <p className="text-sm mb-2">
                                      {targetDaysCount !== null
                                        ? `${targetDaysCount} gün`
                                        : "Belirlenmemiş"}
                                    </p>
                                    {user?.role === "Admin" &&
                                      production.targetDateLastEditedBy && (
                                        <div className="mt-3 pt-2 border-t border-gray-200">
                                          <button
                                            onClick={() =>
                                              handleEditHistoryOpen(
                                                production.id,
                                                "TargetDate",
                                                "Hedef Gün"
                                              )
                                            }
                                            className="flex items-start gap-2 text-xs cursor-pointer p-1 rounded transition-colors w-full text-left"
                                          >
                                            <User2 className="h-3 w-3 text-gray-400 mt-0.5 flex-shrink-0" />
                                            <div className="flex-1 min-w-0">
                                              <div className="flex items-center gap-1 mb-1">
                                                <span className="text-gray-400 font-medium">
                                                  Son düzenleyen:
                                                </span>
                                                <span className="text-gray-200 font-semibold">
                                                  {getUserName(
                                                    production.targetDateLastEditedBy
                                                  )}
                                                </span>
                                              </div>
                                              {production.targetDateLastEditedAt && (
                                                <div className="flex items-center gap-1 text-gray-400">
                                                  <Clock className="h-3 w-3" />
                                                  <span>
                                                    {new Date(
                                                      production.targetDateLastEditedAt
                                                    ).toLocaleDateString(
                                                      "tr-TR",
                                                      {
                                                        day: "2-digit",
                                                        month: "2-digit",
                                                        year: "numeric",
                                                      }
                                                    )}
                                                  </span>
                                                  <span className="text-gray-400">
                                                    •
                                                  </span>
                                                  <span>
                                                    {new Date(
                                                      production.targetDateLastEditedAt
                                                    ).toLocaleTimeString(
                                                      "tr-TR",
                                                      {
                                                        hour: "2-digit",
                                                        minute: "2-digit",
                                                      }
                                                    )}
                                                  </span>
                                                </div>
                                              )}
                                            </div>
                                          </button>
                                        </div>
                                      )}
                                  </div>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                          </td>

                          {/* Teslimat Tarihi */}
                          <td className={`py-3 px-2 hidden sm:table-cell `}>
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <div
                                    className={`flex items-center gap-2 ${
                                      user?.role === "Admin"
                                        ? "cursor-pointer"
                                        : ""
                                    }`}
                                    onClick={
                                      user?.role === "Admin"
                                        ? () =>
                                            handleTargetDateEdit(
                                              production.id,
                                              production.targetDate || ""
                                            )
                                        : undefined
                                    }
                                  >
                                    <span className="text-sm cursor-help">
                                      {formatDate(production.targetDate)}
                                    </span>
                                  </div>
                                </TooltipTrigger>
                                <TooltipContent className="max-w-sm">
                                  <div>
                                    <p className="font-medium mb-1">
                                      Teslimat Tarihi
                                    </p>
                                    <p className="text-sm mb-2">
                                      {formatDate(production.targetDate)}
                                    </p>
                                    {user?.role === "Admin" &&
                                      production.targetDateLastEditedBy && (
                                        <div className="mt-3 pt-2 border-t border-gray-200">
                                          <button
                                            onClick={() =>
                                              handleEditHistoryOpen(
                                                production.id,
                                                "TargetDate",
                                                "Teslimat Tarihi"
                                              )
                                            }
                                            className="flex items-start gap-2 text-xs cursor-pointer p-1 rounded transition-colors w-full text-left"
                                          >
                                            <User2 className="h-3 w-3 text-gray-400 mt-0.5 flex-shrink-0" />
                                            <div className="flex-1 min-w-0">
                                              <div className="flex items-center gap-1 mb-1">
                                                <span className="text-gray-400 font-medium">
                                                  Son düzenleyen:
                                                </span>
                                                <span className="text-gray-200 font-semibold">
                                                  {getUserName(
                                                    production.targetDateLastEditedBy
                                                  )}
                                                </span>
                                              </div>
                                              {production.targetDateLastEditedAt && (
                                                <div className="flex items-center gap-1 text-gray-400">
                                                  <Clock className="h-3 w-3" />
                                                  <span>
                                                    {new Date(
                                                      production.targetDateLastEditedAt
                                                    ).toLocaleDateString(
                                                      "tr-TR",
                                                      {
                                                        day: "2-digit",
                                                        month: "2-digit",
                                                        year: "numeric",
                                                      }
                                                    )}
                                                  </span>
                                                  <span className="text-gray-400">
                                                    •
                                                  </span>
                                                  <span>
                                                    {new Date(
                                                      production.targetDateLastEditedAt
                                                    ).toLocaleTimeString(
                                                      "tr-TR",
                                                      {
                                                        hour: "2-digit",
                                                        minute: "2-digit",
                                                      }
                                                    )}
                                                  </span>
                                                </div>
                                              )}
                                            </div>
                                          </button>
                                        </div>
                                      )}
                                  </div>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                          </td>

                          {/* Kalan Gün */}
                          <td className={`py-3 px-2     `}>
                            {remainingDays !== null ? (
                              <span
                                className={`sm:text-sm text-xs px-2 py-1 rounded ${
                                  remainingDays < 0
                                    ? "bg-red-600 text-white"
                                    : remainingDays <= 2 && remainingDays >= 0
                                    ? "bg-red-500 text-white animate-pulse"
                                    : "bg-green-100 text-green-800"
                                }`}
                              >
                                {remainingDays > 0
                                  ? `${remainingDays} gün`
                                  : remainingDays === 0
                                  ? "Bugün"
                                  : `-${Math.abs(remainingDays)} gün`}
                              </span>
                            ) : (
                              <span className="text-sm text-gray-500">-</span>
                            )}
                          </td>

                          {/* Çoğaltma */}
                          <td className={`py-3 px-2 hidden sm:table-cell  `}>
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <div
                                    className="flex items-center gap-2 justify-center cursor-pointer"
                                    onClick={() =>
                                      handleReplicationEdit(
                                        production.id,
                                        production.replication || 0
                                      )
                                    }
                                  >
                                    <span className="text-sm text-center cursor-help">
                                      {production.replication || 0}
                                    </span>
                                  </div>
                                </TooltipTrigger>
                                <TooltipContent className="max-w-sm">
                                  <div>
                                    <p className="font-medium mb-1">Çoğaltma</p>
                                    <p className="text-sm mb-2">
                                      {production.replication || 0} adet
                                    </p>
                                    {user?.role === "Admin" &&
                                      production.replicationLastEditedBy && (
                                        <div className="mt-3 pt-2 border-t border-gray-200">
                                          <button
                                            onClick={() =>
                                              handleEditHistoryOpen(
                                                production.id,
                                                "Replication",
                                                "Çoğaltma"
                                              )
                                            }
                                            className="flex items-start gap-2 text-xs cursor-pointer p-1 rounded transition-colors w-full text-left"
                                          >
                                            <User2 className="h-3 w-3 text-gray-400 mt-0.5 flex-shrink-0" />
                                            <div className="flex-1 min-w-0">
                                              <div className="flex items-center gap-1 mb-1">
                                                <span className="text-gray-400 font-medium">
                                                  Son düzenleyen:
                                                </span>
                                                <span className="text-gray-200 font-semibold">
                                                  {getUserName(
                                                    production.replicationLastEditedBy
                                                  )}
                                                </span>
                                              </div>
                                              {production.replicationLastEditedAt && (
                                                <div className="flex items-center gap-1 text-gray-400">
                                                  <Clock className="h-3 w-3" />
                                                  <span>
                                                    {new Date(
                                                      production.replicationLastEditedAt
                                                    ).toLocaleDateString(
                                                      "tr-TR",
                                                      {
                                                        day: "2-digit",
                                                        month: "2-digit",
                                                        year: "numeric",
                                                      }
                                                    )}
                                                  </span>
                                                  <span className="text-gray-400">
                                                    •
                                                  </span>
                                                  <span>
                                                    {new Date(
                                                      production.replicationLastEditedAt
                                                    ).toLocaleTimeString(
                                                      "tr-TR",
                                                      {
                                                        hour: "2-digit",
                                                        minute: "2-digit",
                                                      }
                                                    )}
                                                  </span>
                                                </div>
                                              )}
                                            </div>
                                          </button>
                                        </div>
                                      )}
                                  </div>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                          </td>

                          {/* Süreç Takibi */}
                          <td
                            className={`py-3 px-2 hidden sm:table-cell   min-w-[135px]  `}
                          >
                            <div className="flex gap-2 justify-center flex-wrap  ">
                              <TooltipProvider>
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <div className="flex items-center">
                                      <input
                                        type="checkbox"
                                        checked={production.model}
                                        onChange={(e) =>
                                          handleCheckboxChange(
                                            production.id,
                                            "model",
                                            e.target.checked
                                          )
                                        }
                                        className="w-4 h-4 text-green-600"
                                        disabled={
                                          user?.role !== "Admin" &&
                                          user?.role !== "Technician"
                                        }
                                      />
                                    </div>
                                  </TooltipTrigger>
                                  <TooltipContent className="max-w-sm">
                                    <div>
                                      <p className="font-medium mb-1">Model</p>
                                      {user?.role === "Admin" &&
                                        production.modelLastEditedBy && (
                                          <div className="mt-3 pt-2 border-t border-gray-200">
                                            <button
                                              onClick={() =>
                                                handleEditHistoryOpen(
                                                  production.id,
                                                  "Model",
                                                  "Model"
                                                )
                                              }
                                              className="flex items-start gap-2 text-xs cursor-pointer p-1 rounded transition-colors w-full text-left"
                                            >
                                              <User2 className="h-3 w-3 text-gray-400 mt-0.5 flex-shrink-0" />
                                              <div className="flex-1 min-w-0">
                                                <div className="flex items-center gap-1 mb-1">
                                                  <span className="text-gray-400 font-medium">
                                                    Son düzenleyen:
                                                  </span>
                                                  <span className="text-gray-200 font-semibold">
                                                    {getUserName(
                                                      production.modelLastEditedBy
                                                    )}
                                                  </span>
                                                </div>
                                                {production.modelLastEditedAt && (
                                                  <div className="flex items-center gap-1 text-gray-400">
                                                    <Clock className="h-3 w-3" />
                                                    <span>
                                                      {new Date(
                                                        production.modelLastEditedAt
                                                      ).toLocaleDateString(
                                                        "tr-TR",
                                                        {
                                                          day: "2-digit",
                                                          month: "2-digit",
                                                          year: "numeric",
                                                        }
                                                      )}
                                                    </span>
                                                    <span className="text-gray-400">
                                                      •
                                                    </span>
                                                    <span>
                                                      {new Date(
                                                        production.modelLastEditedAt
                                                      ).toLocaleTimeString(
                                                        "tr-TR",
                                                        {
                                                          hour: "2-digit",
                                                          minute: "2-digit",
                                                        }
                                                      )}
                                                    </span>
                                                  </div>
                                                )}
                                              </div>
                                            </button>
                                          </div>
                                        )}
                                    </div>
                                  </TooltipContent>
                                </Tooltip>
                              </TooltipProvider>
                              <TooltipProvider>
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <div className="flex items-center">
                                      <input
                                        type="checkbox"
                                        checked={production.platePressing}
                                        onChange={(e) =>
                                          handleCheckboxChange(
                                            production.id,
                                            "platePressing",
                                            e.target.checked
                                          )
                                        }
                                        className="w-4 h-4 text-yellow-600"
                                        disabled={
                                          user?.role !== "Admin" &&
                                          (user?.role !== "Technician" ||
                                            production.assignedUserId !==
                                              user?.id)
                                        }
                                      />
                                    </div>
                                  </TooltipTrigger>
                                  <TooltipContent className="max-w-sm">
                                    <div>
                                      <p className="font-medium mb-1">
                                        Plak Basma
                                      </p>
                                      {user?.role === "Admin" &&
                                        production.platePressingLastEditedBy && (
                                          <div className="mt-3 pt-2 border-t border-gray-200">
                                            <button
                                              onClick={() =>
                                                handleEditHistoryOpen(
                                                  production.id,
                                                  "PlatePressing",
                                                  "Plak Basma"
                                                )
                                              }
                                              className="flex items-start gap-2 text-xs cursor-pointer p-1 rounded transition-colors w-full text-left"
                                            >
                                              <User2 className="h-3 w-3 text-gray-400 mt-0.5 flex-shrink-0" />
                                              <div className="flex-1 min-w-0">
                                                <div className="flex items-center gap-1 mb-1">
                                                  <span className="text-gray-400 font-medium">
                                                    Son düzenleyen:
                                                  </span>
                                                  <span className="text-gray-200 font-semibold">
                                                    {getUserName(
                                                      production.platePressingLastEditedBy
                                                    )}
                                                  </span>
                                                </div>
                                                {production.platePressingLastEditedAt && (
                                                  <div className="flex items-center gap-1 text-gray-400">
                                                    <Clock className="h-3 w-3" />
                                                    <span>
                                                      {new Date(
                                                        production.platePressingLastEditedAt
                                                      ).toLocaleDateString(
                                                        "tr-TR",
                                                        {
                                                          day: "2-digit",
                                                          month: "2-digit",
                                                          year: "numeric",
                                                        }
                                                      )}
                                                    </span>
                                                    <span className="text-gray-400">
                                                      •
                                                    </span>
                                                    <span>
                                                      {new Date(
                                                        production.platePressingLastEditedAt
                                                      ).toLocaleTimeString(
                                                        "tr-TR",
                                                        {
                                                          hour: "2-digit",
                                                          minute: "2-digit",
                                                        }
                                                      )}
                                                    </span>
                                                  </div>
                                                )}
                                              </div>
                                            </button>
                                          </div>
                                        )}
                                    </div>
                                  </TooltipContent>
                                </Tooltip>
                              </TooltipProvider>
                              <TooltipProvider>
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <div className="flex items-center">
                                      <input
                                        type="checkbox"
                                        checked={production.fineCut}
                                        onChange={(e) =>
                                          handleCheckboxChange(
                                            production.id,
                                            "fineCut",
                                            e.target.checked
                                          )
                                        }
                                        className="w-4 h-4 text-orange-600"
                                        disabled={
                                          user?.role !== "Admin" &&
                                          (user?.role !== "Technician" ||
                                            production.assignedUserId !==
                                              user?.id)
                                        }
                                      />
                                    </div>
                                  </TooltipTrigger>
                                  <TooltipContent className="max-w-sm">
                                    <div>
                                      <p className="font-medium mb-1">
                                        İnce Kesim
                                      </p>
                                      {user?.role === "Admin" &&
                                        production.fineCutLastEditedBy && (
                                          <div className="mt-3 pt-2 border-t border-gray-200">
                                            <button
                                              onClick={() =>
                                                handleEditHistoryOpen(
                                                  production.id,
                                                  "FineCut",
                                                  "İnce Kesim"
                                                )
                                              }
                                              className="flex items-start gap-2 text-xs cursor-pointer p-1 rounded transition-colors w-full text-left"
                                            >
                                              <User2 className="h-3 w-3 text-gray-400 mt-0.5 flex-shrink-0" />
                                              <div className="flex-1 min-w-0">
                                                <div className="flex items-center gap-1 mb-1">
                                                  <span className="text-gray-400 font-medium">
                                                    Son düzenleyen:
                                                  </span>
                                                  <span className="text-gray-200 font-semibold">
                                                    {getUserName(
                                                      production.fineCutLastEditedBy
                                                    )}
                                                  </span>
                                                </div>
                                                {production.fineCutLastEditedAt && (
                                                  <div className="flex items-center gap-1 text-gray-400">
                                                    <Clock className="h-3 w-3" />
                                                    <span>
                                                      {new Date(
                                                        production.fineCutLastEditedAt
                                                      ).toLocaleDateString(
                                                        "tr-TR",
                                                        {
                                                          day: "2-digit",
                                                          month: "2-digit",
                                                          year: "numeric",
                                                        }
                                                      )}
                                                    </span>
                                                    <span className="text-gray-400">
                                                      •
                                                    </span>
                                                    <span>
                                                      {new Date(
                                                        production.fineCutLastEditedAt
                                                      ).toLocaleTimeString(
                                                        "tr-TR",
                                                        {
                                                          hour: "2-digit",
                                                          minute: "2-digit",
                                                        }
                                                      )}
                                                    </span>
                                                  </div>
                                                )}
                                              </div>
                                            </button>
                                          </div>
                                        )}
                                    </div>
                                  </TooltipContent>
                                </Tooltip>
                              </TooltipProvider>
                              <TooltipProvider>
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <div className="flex items-center">
                                      <input
                                        type="checkbox"
                                        checked={production.packaging}
                                        onChange={(e) =>
                                          handleCheckboxChange(
                                            production.id,
                                            "packaging",
                                            e.target.checked
                                          )
                                        }
                                        className="w-4 h-4 text-purple-600"
                                        disabled={
                                          user?.role !== "Admin" &&
                                          (user?.role !== "Technician" ||
                                            production.assignedUserId !==
                                              user?.id)
                                        }
                                      />
                                    </div>
                                  </TooltipTrigger>
                                  <TooltipContent className="max-w-sm">
                                    <div>
                                      <p className="font-medium mb-1">
                                        Paketleme
                                      </p>
                                      {user?.role === "Admin" &&
                                        production.packagingLastEditedBy && (
                                          <div className="mt-3 pt-2 border-t border-gray-200">
                                            <button
                                              onClick={() =>
                                                handleEditHistoryOpen(
                                                  production.id,
                                                  "Packaging",
                                                  "Paketleme"
                                                )
                                              }
                                              className="flex items-start gap-2 text-xs cursor-pointer p-1 rounded transition-colors w-full text-left"
                                            >
                                              <User2 className="h-3 w-3 text-gray-400 mt-0.5 flex-shrink-0" />
                                              <div className="flex-1 min-w-0">
                                                <div className="flex items-center gap-1 mb-1">
                                                  <span className="text-gray-400 font-medium">
                                                    Son düzenleyen:
                                                  </span>
                                                  <span className="text-gray-200 font-semibold">
                                                    {getUserName(
                                                      production.packagingLastEditedBy
                                                    )}
                                                  </span>
                                                </div>
                                                {production.packagingLastEditedAt && (
                                                  <div className="flex items-center gap-1 text-gray-400">
                                                    <Clock className="h-3 w-3" />
                                                    <span>
                                                      {new Date(
                                                        production.packagingLastEditedAt
                                                      ).toLocaleDateString(
                                                        "tr-TR",
                                                        {
                                                          day: "2-digit",
                                                          month: "2-digit",
                                                          year: "numeric",
                                                        }
                                                      )}
                                                    </span>
                                                    <span className="text-gray-400">
                                                      •
                                                    </span>
                                                    <span>
                                                      {new Date(
                                                        production.packagingLastEditedAt
                                                      ).toLocaleTimeString(
                                                        "tr-TR",
                                                        {
                                                          hour: "2-digit",
                                                          minute: "2-digit",
                                                        }
                                                      )}
                                                    </span>
                                                  </div>
                                                )}
                                              </div>
                                            </button>
                                          </div>
                                        )}
                                    </div>
                                  </TooltipContent>
                                </Tooltip>
                              </TooltipProvider>
                              <TooltipProvider>
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <div className="flex items-center">
                                      <input
                                        type="checkbox"
                                        checked={production.shipping}
                                        onChange={(e) =>
                                          handleCheckboxChange(
                                            production.id,
                                            "shipping",
                                            e.target.checked
                                          )
                                        }
                                        className="w-4 h-4 text-red-600"
                                        disabled={
                                          user?.role !== "Admin" &&
                                          (user?.role !== "Technician" ||
                                            production.assignedUserId !==
                                              user?.id)
                                        }
                                      />
                                    </div>
                                  </TooltipTrigger>
                                  <TooltipContent className="max-w-sm">
                                    <div>
                                      <p className="font-medium mb-1">Kargo</p>
                                      {user?.role === "Admin" &&
                                        production.shippingLastEditedBy && (
                                          <div className="mt-3 pt-2 border-t border-gray-200">
                                            <button
                                              onClick={() =>
                                                handleEditHistoryOpen(
                                                  production.id,
                                                  "Shipping",
                                                  "Kargo"
                                                )
                                              }
                                              className="flex items-start gap-2 text-xs cursor-pointer p-1 rounded transition-colors w-full text-left"
                                            >
                                              <User2 className="h-3 w-3 text-gray-400 mt-0.5 flex-shrink-0" />
                                              <div className="flex-1 min-w-0">
                                                <div className="flex items-center gap-1 mb-1">
                                                  <span className="text-gray-400 font-medium">
                                                    Son düzenleyen:
                                                  </span>
                                                  <span className="text-gray-200 font-semibold">
                                                    {getUserName(
                                                      production.shippingLastEditedBy
                                                    )}
                                                  </span>
                                                </div>
                                                {production.shippingLastEditedAt && (
                                                  <div className="flex items-center gap-1 text-gray-400">
                                                    <Clock className="h-3 w-3" />
                                                    <span>
                                                      {new Date(
                                                        production.shippingLastEditedAt
                                                      ).toLocaleDateString(
                                                        "tr-TR",
                                                        {
                                                          day: "2-digit",
                                                          month: "2-digit",
                                                          year: "numeric",
                                                        }
                                                      )}
                                                    </span>
                                                    <span className="text-gray-400">
                                                      •
                                                    </span>
                                                    <span>
                                                      {new Date(
                                                        production.shippingLastEditedAt
                                                      ).toLocaleTimeString(
                                                        "tr-TR",
                                                        {
                                                          hour: "2-digit",
                                                          minute: "2-digit",
                                                        }
                                                      )}
                                                    </span>
                                                  </div>
                                                )}
                                              </div>
                                            </button>
                                          </div>
                                        )}
                                    </div>
                                  </TooltipContent>
                                </Tooltip>
                              </TooltipProvider>
                            </div>
                          </td>

                          {/* Teknisyen Görev Atama */}
                          <td className={`py-3 px-2    max-w-[100px]`}>
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <div className="flex items-center justify-center">
                                    {user?.role === "Admin" ? (
                                      // Admin görünümü - dropdown ile teknisyen seçimi
                                      production.assignedUserId ? (
                                        <Select
                                          value={production.assignedUserId}
                                          onValueChange={(newUserId) => {
                                            if (newUserId === "none") {
                                              unassignTask(production.id);
                                            } else {
                                              reassignTask(
                                                production.id,
                                                newUserId
                                              );
                                            }
                                          }}
                                        >
                                          <SelectTrigger className="w-full h-8 text-xs">
                                            <SelectValue>
                                              {(() => {
                                                const tech = technicians.find(
                                                  (t) =>
                                                    t.id ===
                                                    production.assignedUserId
                                                );
                                                return tech ? (
                                                  <div className="flex items-center gap-2 w-full">
                                                    <span className="inline-flex items-center justify-center w-5 h-5 rounded-full bg-green-100 text-green-700 font-bold text-xs">
                                                      {getInitials(
                                                        tech.fullName
                                                      )}
                                                    </span>
                                                    <span className="truncate text-xs">
                                                      {tech.fullName}
                                                    </span>
                                                  </div>
                                                ) : (
                                                  "Atanmış"
                                                );
                                              })()}
                                            </SelectValue>
                                          </SelectTrigger>
                                          <SelectContent>
                                            {technicians.map((tech) => (
                                              <SelectItem
                                                key={tech.id}
                                                value={tech.id}
                                              >
                                                <div className="flex items-center gap-2">
                                                  <span className="inline-flex items-center justify-center w-5 h-5 rounded-full bg-gray-100 text-gray-700 font-bold text-xs">
                                                    {getInitials(tech.fullName)}
                                                  </span>
                                                  {tech.fullName}
                                                </div>
                                              </SelectItem>
                                            ))}
                                            <SelectItem value="none">
                                              Atamayı Kaldır
                                            </SelectItem>
                                          </SelectContent>
                                        </Select>
                                      ) : (
                                        <Select
                                          onValueChange={(userId) => {
                                            if (userId) {
                                              reassignTask(
                                                production.id,
                                                userId
                                              );
                                            }
                                          }}
                                        >
                                          <SelectTrigger className="w-full h-8 text-xs">
                                            <SelectValue placeholder="Teknisyen Seç" />
                                          </SelectTrigger>
                                          <SelectContent>
                                            {technicians.map((tech) => (
                                              <SelectItem
                                                key={tech.id}
                                                value={tech.id}
                                              >
                                                <div className="flex items-center gap-2">
                                                  <span className="inline-flex items-center justify-center w-5 h-5 rounded-full bg-gray-100 text-gray-700 font-bold text-xs">
                                                    {getInitials(tech.fullName)}
                                                  </span>
                                                  {tech.fullName}
                                                </div>
                                              </SelectItem>
                                            ))}
                                          </SelectContent>
                                        </Select>
                                      )
                                    ) : user?.role === "Technician" ? (
                                      // Teknisyen görünümü
                                      <>
                                        {isMobile() && (
                                          <div className="flex items-center gap-2">
                                            <Button
                                              variant="outline"
                                              size="sm"
                                              className={`h-8 w-auto px-2 text-blue-600 border-blue-200 hover:bg-blue-50
                                        ${
                                          production.assignedUserId === user?.id
                                            ? "border-2 border-green-500 text-green-500"
                                            : ""
                                        }`}
                                              onClick={() =>
                                                router.push(
                                                  `/tasks/task-detail/${production.id}`
                                                )
                                              }
                                            >
                                              <span className="sm:text-sm text-xs">
                                                Detaya Git
                                              </span>
                                              {production.adminNote && (
                                                <span className="ml-1 text-red-500 font-bold">
                                                  !
                                                </span>
                                              )}
                                            </Button>

                                            {/* Görevi al veya bırak butonu */}
                                            {/* {production.assignedUserId === user?.id ? (
                                      <Button
                                        variant="outline"
                                        size="sm"
                                        onClick={() =>
                                          unassignTask(production.id)
                                        }
                                        className="h-8 w-auto px-2 text-red-600 border-red-200 hover:bg-red-50"
                                      >
                                        <span className="sm:text-sm text-xs">
                                          Görevi Bırak
                                        </span>
                                      </Button>
                                    ) : !production.assignedUserId ? (
                                      <Button
                                        variant="outline"
                                        size="sm"
                                        onClick={() =>
                                          handleTaskAssign(production.id)
                                        }
                                        className="h-8 w-auto px-2 text-green-600 border-green-200 hover:bg-green-50"
                                      >
                                        <span className="sm:text-sm text-xs">
                                          Görevi Al
                                        </span>
                                      </Button>
                                    ) : (
                                      <span className="text-xs text-gray-500 flex items-center gap-1">
                                        <span className="inline-flex items-center justify-center w-4 h-4 rounded-full bg-gray-100 text-gray-700 font-bold text-xs">
                                          {getInitials(
                                            technicians.find(
                                              (t) =>
                                                t.id ===
                                                production.assignedUserId
                                            )?.fullName || ""
                                          )}
                                        </span>
                                        {technicians.find(
                                          (t) =>
                                            t.id === production.assignedUserId
                                        )?.fullName || "Atanmış"}
                                      </span>
                                    )} */}
                                          </div>
                                        )}

                                        {/* Desktop versiyonu - değişiklik yok */}
                                        {!isMobile() && (
                                          <>
                                            {production.assignedUserId ===
                                            user?.id ? (
                                              <TooltipProvider>
                                                <Tooltip>
                                                  <TooltipTrigger asChild>
                                                    <Button
                                                      variant="outline"
                                                      size="sm"
                                                      onClick={() =>
                                                        unassignTask(
                                                          production.id
                                                        )
                                                      }
                                                      className="h-8 w-8 p-0"
                                                    >
                                                      <UserX className="h-4 w-4 text-red-500" />
                                                    </Button>
                                                  </TooltipTrigger>
                                                  <TooltipContent>
                                                    <p>Görevi Bırak</p>
                                                  </TooltipContent>
                                                </Tooltip>
                                              </TooltipProvider>
                                            ) : !production.assignedUserId ? (
                                              <TooltipProvider>
                                                <Tooltip>
                                                  <TooltipTrigger asChild>
                                                    <Button
                                                      variant="outline"
                                                      size="sm"
                                                      onClick={() =>
                                                        handleTaskAssign(
                                                          production.id
                                                        )
                                                      }
                                                      className="h-8 w-8 p-0"
                                                    >
                                                      <UserCheck className="h-4 w-4 text-green-500" />
                                                    </Button>
                                                  </TooltipTrigger>
                                                  <TooltipContent>
                                                    <p>Görevi Al</p>
                                                  </TooltipContent>
                                                </Tooltip>
                                              </TooltipProvider>
                                            ) : (
                                              <span className="text-xs text-gray-500 flex items-center gap-1">
                                                <span className="inline-flex items-center justify-center w-4 h-4 rounded-full bg-gray-100 text-gray-700 font-bold text-xs">
                                                  {getInitials(
                                                    technicians.find(
                                                      (t) =>
                                                        t.id ===
                                                        production.assignedUserId
                                                    )?.fullName || ""
                                                  )}
                                                </span>
                                                {technicians.find(
                                                  (t) =>
                                                    t.id ===
                                                    production.assignedUserId
                                                )?.fullName || "Atanmış"}
                                              </span>
                                            )}
                                          </>
                                        )}
                                      </>
                                    ) : // Diğer roller için görünüm
                                    production.assignedUserId ? (
                                      <span className="text-xs text-green-600 flex items-center gap-1">
                                        <span className="inline-flex items-center justify-center w-6 h-6 rounded-full bg-green-100 text-green-700 font-bold">
                                          {getInitials(
                                            technicians.find(
                                              (t) =>
                                                t.id ===
                                                production.assignedUserId
                                            )?.fullName || ""
                                          )}
                                        </span>
                                        {technicians.find(
                                          (t) =>
                                            t.id === production.assignedUserId
                                        )?.fullName || "Atanmış"}
                                      </span>
                                    ) : (
                                      <span className="text-xs text-gray-500">
                                        Atanmamış
                                      </span>
                                    )}
                                  </div>
                                </TooltipTrigger>
                                <TooltipContent className="max-w-sm">
                                  <div>
                                    <p className="font-medium mb-1">
                                      Teknisyen Görevi
                                    </p>
                                    {user?.role === "Admin" &&
                                      production.assignedUserIdLastEditedBy && (
                                        <div className="mt-3 pt-2 border-t border-gray-200">
                                          <button
                                            onClick={() =>
                                              handleEditHistoryOpen(
                                                production.id,
                                                "AssignedUserId",
                                                "Teknisyen Görevi"
                                              )
                                            }
                                            className="flex items-start gap-2 text-xs cursor-pointer p-1 rounded transition-colors w-full text-left"
                                          >
                                            <User2 className="h-3 w-3 text-gray-400 mt-0.5 flex-shrink-0" />
                                            <div className="flex-1 min-w-0">
                                              <div className="flex items-center gap-1 mb-1">
                                                <span className="text-gray-400 font-medium">
                                                  Son düzenleyen:
                                                </span>
                                                <span className="text-gray-200 font-semibold">
                                                  {getUserName(
                                                    production.assignedUserIdLastEditedBy
                                                  )}
                                                </span>
                                              </div>
                                              {production.assignedUserIdLastEditedAt && (
                                                <div className="flex items-center gap-1 text-gray-400">
                                                  <Clock className="h-3 w-3" />
                                                  <span>
                                                    {new Date(
                                                      production.assignedUserIdLastEditedAt
                                                    ).toLocaleDateString(
                                                      "tr-TR",
                                                      {
                                                        day: "2-digit",
                                                        month: "2-digit",
                                                        year: "numeric",
                                                      }
                                                    )}
                                                  </span>
                                                  <span className="text-gray-400">
                                                    •
                                                  </span>
                                                  <span>
                                                    {new Date(
                                                      production.assignedUserIdLastEditedAt
                                                    ).toLocaleTimeString(
                                                      "tr-TR",
                                                      {
                                                        hour: "2-digit",
                                                        minute: "2-digit",
                                                      }
                                                    )}
                                                  </span>
                                                </div>
                                              )}
                                            </div>
                                          </button>
                                        </div>
                                      )}
                                  </div>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                          </td>

                          {/* Açıklama */}
                          <td
                            className={`py-3 px-2 hidden sm:table-cell w-[100px] max-w-[100px]`}
                          >
                            <div
                              className={`flex items-center gap-2 ${
                                user?.role === "Admin" ? "cursor-pointer" : ""
                              }`}
                              onClick={
                                user?.role === "Admin"
                                  ? () =>
                                      handleNotesEdit(
                                        production.id,
                                        production.notes || ""
                                      )
                                  : undefined
                              }
                            >
                              <TooltipProvider>
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <span
                                      className="text-sm text-gray-600 overflow-hidden break-words w-full cursor-help"
                                      style={{
                                        display: "-webkit-box",
                                        WebkitLineClamp: 2,
                                        WebkitBoxOrient: "vertical",
                                        textOverflow: "ellipsis",
                                      }}
                                    >
                                      {production.notes || "-"}
                                    </span>
                                  </TooltipTrigger>
                                  {production.notes && (
                                    <TooltipContent className="whitespace-pre-line break-words min-w-[200px] max-w-[400px]">
                                      <div>
                                        <p className="font-medium mb-2">
                                          Açıklama
                                        </p>
                                        <p className="text-sm mb-2 whitespace-pre-wrap">
                                          {production.notes}
                                        </p>
                                        {user?.role === "Admin" &&
                                          production.notesLastEditedBy && (
                                            <div className="mt-3 pt-2 border-t border-gray-200">
                                              <button
                                                onClick={() =>
                                                  handleEditHistoryOpen(
                                                    production.id,
                                                    "Notes",
                                                    "Açıklama"
                                                  )
                                                }
                                                className="flex items-start gap-2 text-xs cursor-pointer p-1 rounded transition-colors w-full text-left"
                                              >
                                                <User2 className="h-3 w-3 text-gray-400 mt-0.5 flex-shrink-0" />
                                                <div className="flex-1 min-w-0">
                                                  <div className="flex items-center gap-1 mb-1">
                                                    <span className="text-gray-400 font-medium">
                                                      Son düzenleyen:
                                                    </span>
                                                    <span className="text-gray-200 font-semibold">
                                                      {getUserName(
                                                        production.notesLastEditedBy
                                                      )}
                                                    </span>
                                                  </div>
                                                  {production.notesLastEditedAt && (
                                                    <div className="flex items-center gap-1 text-gray-400">
                                                      <Clock className="h-3 w-3" />
                                                      <span>
                                                        {new Date(
                                                          production.notesLastEditedAt
                                                        ).toLocaleDateString(
                                                          "tr-TR",
                                                          {
                                                            day: "2-digit",
                                                            month: "2-digit",
                                                            year: "numeric",
                                                          }
                                                        )}
                                                      </span>
                                                      <span className="text-gray-400">
                                                        •
                                                      </span>
                                                      <span>
                                                        {new Date(
                                                          production.notesLastEditedAt
                                                        ).toLocaleTimeString(
                                                          "tr-TR",
                                                          {
                                                            hour: "2-digit",
                                                            minute: "2-digit",
                                                          }
                                                        )}
                                                      </span>
                                                    </div>
                                                  )}
                                                </div>
                                              </button>
                                            </div>
                                          )}
                                      </div>
                                    </TooltipContent>
                                  )}
                                </Tooltip>
                              </TooltipProvider>
                            </div>
                          </td>
                          {/* İşlemler */}
                          {user?.role === "Admin" && (
                            <td className="hidden sm:table-cell py-3 px-1     ">
                              <div className="flex gap-1 justify-center  w-full ">
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => handleArchive(production.id)}
                                  className="h-8 w-8 p-0 text-blue-600 hover:text-blue-700"
                                >
                                  <Archive className="h-4 w-4" />
                                </Button>
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => handleDelete(production.id)}
                                  className="h-8 w-8 p-0 text-red-600 hover:text-red-700"
                                >
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() =>
                                    handleAdminNoteEdit(
                                      production.id,
                                      production.adminNote || ""
                                    )
                                  }
                                  className="h-8 w-8 p-0 text-yellow-600 hover:text-yellow-700"
                                >
                                  <NotebookPen className="h-4 w-4" />
                                </Button>
                              </div>
                            </td>
                          )}
                        </tr>
                      );
                    })}
                  </tbody>
                </table>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Name Edit Modal */}
        {editingNameId && (
          <div className="fixed inset-0 bg-black/50 bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
              <h3 className="text-lg font-semibold mb-4">Ad Soyad Düzenle</h3>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Ad
                  </label>
                  <input
                    type="text"
                    value={nameModalData.name}
                    onChange={(e) =>
                      setNameModalData({
                        ...nameModalData,
                        name: e.target.value,
                      })
                    }
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Ad girin..."
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Soyad
                  </label>
                  <input
                    type="text"
                    value={nameModalData.surname}
                    onChange={(e) =>
                      setNameModalData({
                        ...nameModalData,
                        surname: e.target.value,
                      })
                    }
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Soyad girin..."
                  />
                </div>
              </div>
              <div className="flex gap-2 mt-6">
                <Button onClick={handleNameSave} className="flex-1">
                  <Save className="h-4 w-4 mr-2" />
                  Kaydet
                </Button>
                <Button
                  variant="outline"
                  onClick={handleNameCancel}
                  className="flex-1"
                >
                  <X className="h-4 w-4 mr-2" />
                  İptal
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Date Edit Modal */}
        {editingDateId && (
          <div className="fixed inset-0 bg-black/50 bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
              <h3 className="text-lg font-semibold mb-4">
                Onay Tarihi Düzenle
              </h3>
              <input
                type="date"
                value={tempDateValue}
                onChange={(e) => setTempDateValue(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
              <div className="flex gap-2 mt-4">
                <Button onClick={handleDateSave} className="flex-1">
                  <Save className="h-4 w-4 mr-2" />
                  Kaydet
                </Button>
                <Button
                  variant="outline"
                  onClick={handleDateCancel}
                  className="flex-1"
                >
                  <X className="h-4 w-4 mr-2" />
                  İptal
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Target Date Edit Modal */}
        {editingTargetDateId && (
          <div className="fixed inset-0 bg-black/50 bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
              <h3 className="text-lg font-semibold mb-4">
                Teslimat Tarihi Düzenle
              </h3>
              <input
                type="date"
                value={tempTargetDateValue}
                onChange={(e) => setTempTargetDateValue(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
              <div className="flex gap-2 mt-4">
                <Button onClick={handleTargetDateSave} className="flex-1">
                  <Save className="h-4 w-4 mr-2" />
                  Kaydet
                </Button>
                <Button
                  variant="outline"
                  onClick={handleTargetDateCancel}
                  className="flex-1"
                >
                  <X className="h-4 w-4 mr-2" />
                  İptal
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Target Days Edit Modal */}
        {editingTargetDaysId && (
          <div className="fixed inset-0 bg-black/50 bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
              <h3 className="text-lg font-semibold mb-4">Hedef Gün Düzenle</h3>
              <div className="space-y-2 mb-4">
                <label className="block text-sm font-medium text-gray-700">
                  Kaç gün sonra teslim edilecek?
                </label>
                <input
                  type="number"
                  value={tempTargetDaysValue}
                  onChange={(e) => setTempTargetDaysValue(e.target.value)}
                  min="0"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Gün sayısını girin..."
                />
                <p className="text-sm text-gray-500">
                  Onay tarihinden itibaren bu kadar gün sonra teslimat tarihi
                  otomatik hesaplanacak.
                </p>
              </div>
              <div className="flex gap-2 mt-4">
                <Button onClick={handleTargetDaysSave} className="flex-1">
                  <Save className="h-4 w-4 mr-2" />
                  Kaydet
                </Button>
                <Button
                  variant="outline"
                  onClick={handleTargetDaysCancel}
                  className="flex-1"
                >
                  <X className="h-4 w-4 mr-2" />
                  İptal
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Replication Edit Modal */}
        {editingReplicationId && (
          <div className="fixed inset-0 bg-black/50 bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
              <h3 className="text-lg font-semibold mb-4">
                Çoğaltma Sayısı Düzenle
              </h3>
              <input
                type="number"
                value={tempReplicationValue}
                onChange={(e) => setTempReplicationValue(e.target.value)}
                min="0"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Çoğaltma sayısını girin..."
              />
              <div className="flex gap-2 mt-4">
                <Button onClick={handleReplicationSave} className="flex-1">
                  <Save className="h-4 w-4 mr-2" />
                  Kaydet
                </Button>
                <Button
                  variant="outline"
                  onClick={handleReplicationCancel}
                  className="flex-1"
                >
                  <X className="h-4 w-4 mr-2" />
                  İptal
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Notes Edit Modal */}
        {editingNotesId && (
          <div className="fixed inset-0 bg-black/50 bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
              <h3 className="text-lg font-semibold mb-4">Açıklama Düzenle</h3>
              <textarea
                value={notesModalData}
                onChange={(e) => setNotesModalData(e.target.value)}
                className="w-full h-32 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                placeholder="Açıklama girin..."
              />
              <div className="flex gap-2 mt-4">
                <Button onClick={handleNotesSave} className="flex-1">
                  <Save className="h-4 w-4 mr-2" />
                  Kaydet
                </Button>
                <Button
                  variant="outline"
                  onClick={handleNotesCancel}
                  className="flex-1"
                >
                  <X className="h-4 w-4 mr-2" />
                  İptal
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Delete Confirmation Modal */}
        <Dialog open={deleteConfirmId !== null} onOpenChange={cancelDelete}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Üretimi Sil</DialogTitle>
              <DialogDescription>
                Bu üretimi silmek istediğinizden emin misiniz? Bu işlem geri
                alınamaz.
              </DialogDescription>
            </DialogHeader>
            <DialogFooter>
              <Button variant="outline" onClick={cancelDelete}>
                İptal
              </Button>
              <Button variant="destructive" onClick={confirmDelete}>
                Sil
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Archive Confirmation Modal */}
        <Dialog open={archiveConfirmId !== null} onOpenChange={cancelArchive}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Üretimi Arşivle</DialogTitle>
              <DialogDescription>
                Bu üretimi arşive almak istediğinizden emin misiniz? Arşivlenen
                üretimler plan arşiv bölümünde görüntülenebilir.
              </DialogDescription>
            </DialogHeader>
            <DialogFooter>
              <Button variant="outline" onClick={cancelArchive}>
                İptal
              </Button>
              <Button onClick={confirmArchive}>Arşivle</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Task Assignment Confirmation Modal */}
        <Dialog open={assignConfirmId !== null} onOpenChange={cancelTaskAssign}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Görev Alma Onayı</DialogTitle>
              <DialogDescription>
                Bu görevi almak istediğinizden emin misiniz? Görev size atanacak
                ve süreç takibi kısmını sadece siz düzenleyebileceksiniz.
              </DialogDescription>
            </DialogHeader>
            <DialogFooter>
              <Button variant="outline" onClick={cancelTaskAssign}>
                İptal
              </Button>
              <Button onClick={confirmTaskAssign}>Görevi Al</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Admin Note Modal */}
        {adminNoteModal.open && (
          <div className="fixed inset-0 bg-black/50 bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
              <h3 className="text-lg font-semibold mb-4">
                Not {adminNoteModal.isEdit ? "Düzenle" : "Görüntüle"}
              </h3>
              <textarea
                value={adminNoteModal.note}
                onChange={(e) =>
                  setAdminNoteModal((modal) => ({
                    ...modal,
                    note: e.target.value,
                  }))
                }
                className="w-full h-32 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                placeholder="Not girin..."
                readOnly={!adminNoteModal.isEdit}
              />
              <div className="flex gap-2 mt-4">
                {adminNoteModal.isEdit ? (
                  <Button onClick={handleAdminNoteSave} className="flex-1">
                    <Save className="h-4 w-4 mr-2" />
                    Kaydet
                  </Button>
                ) : null}
                {adminNoteModal.isEdit && adminNoteModal.note && (
                  <Button
                    variant="destructive"
                    onClick={handleAdminNoteDelete}
                    className="flex-1"
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    Notu Sil
                  </Button>
                )}
                <Button
                  variant="outline"
                  onClick={() =>
                    setAdminNoteModal({
                      open: false,
                      productionId: null,
                      note: "",
                      isEdit: false,
                    })
                  }
                  className="flex-1"
                >
                  <X className="h-4 w-4 mr-2" />
                  Kapat
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Edit History Modal */}
        <EditHistoryModal
          open={editHistoryModal.open}
          onClose={handleEditHistoryClose}
          productionId={editHistoryModal.productionId || 0}
          fieldName={editHistoryModal.fieldName}
          fieldDisplayName={editHistoryModal.fieldDisplayName}
          getUserName={getUserName}
        />
      </div>
    </TooltipProvider>
  );
}
