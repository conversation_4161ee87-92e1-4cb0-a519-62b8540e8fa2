import {
  LoginDto,
  RegisterDto,
  AuthResponse,
  User,
  UpdateUserDto,
  UpdateUserRoleDto,
  CreateUserDto,
  Role,
  CreateRoleDto,
  PaginationData,
  PaginationQuery,
  Production,
  ElegooPrinter,
  CreateElegooPrinterDto,
  UpdateElegooPrinterDto,
  PrinterIssue,
  CreatePrinterIssueDto,
  UpdatePrinterIssueDto,
  PrinterMaintenance,
  CreatePrinterMaintenanceDto,
  UpdatePrinterMaintenanceDto,
} from "./types";
import { useAuthStore } from "@/stores/authStore";

class ApiError extends Error {
  constructor(public status: number, message: string) {
    super(message);
    this.name = "ApiError";
  }
}

class ApiService {
  private baseURL: string;

  constructor() {
    this.baseURL =
      process.env.NEXT_PUBLIC_API_URL || "https://lab.crystalaligner.com/api";
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const token = useAuthStore.getState().token;

    const config: RequestInit = {
      headers: {
        "Content-Type": "application/json",
        ...(token && { Authorization: `Bearer ${token}` }),
        ...options.headers,
      },
      ...options,
    };

    try {
      const response = await fetch(`${this.baseURL}${endpoint}`, config);

      if (response.status === 401) {
        // Token expired, try to refresh
        const refreshed = await this.tryRefreshToken();
        if (refreshed) {
          // Retry the original request with new token
          const newToken = useAuthStore.getState().token;
          config.headers = {
            ...config.headers,
            Authorization: `Bearer ${newToken}`,
          };
          const retryResponse = await fetch(
            `${this.baseURL}${endpoint}`,
            config
          );
          if (!retryResponse.ok) {
            throw new ApiError(
              retryResponse.status,
              await retryResponse.text()
            );
          }
          return await retryResponse.json();
        } else {
          // Refresh failed, logout user
          useAuthStore.getState().logout();
          throw new ApiError(401, "Authentication failed");
        }
      }

      if (!response.ok) {
        throw new ApiError(response.status, await response.text());
      }

      return await response.json();
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError(500, "Network error");
    }
  }

  private async tryRefreshToken(): Promise<boolean> {
    try {
      const refreshToken = useAuthStore.getState().refreshToken;
      if (!refreshToken) {
        return false;
      }

      const response = await fetch(`${this.baseURL}/auth/refresh`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ refreshToken }),
      });

      if (response.ok) {
        const data: AuthResponse = await response.json();
        useAuthStore.getState().login(
          {
            id: data.username,
            userName: data.username,
            email: data.email,
            fullName: data.fullName,
            role: data.role,
            createdAt: new Date().toISOString(),
            isActive: true,
          },
          data.token,
          data.refreshToken
        );
        return true;
      }
      return false;
    } catch {
      return false;
    }
  }

  // Auth endpoints
  async login(credentials: LoginDto): Promise<AuthResponse> {
    return this.request<AuthResponse>("/auth/login", {
      method: "POST",
      body: JSON.stringify(credentials),
    });
  }

  async register(userData: RegisterDto): Promise<AuthResponse> {
    return this.request<AuthResponse>("/auth/register", {
      method: "POST",
      body: JSON.stringify(userData),
    });
  }

  async refreshToken(refreshToken: string): Promise<AuthResponse> {
    return this.request<AuthResponse>("/auth/refresh", {
      method: "POST",
      body: JSON.stringify({ refreshToken }),
    });
  }

  async revokeToken(): Promise<void> {
    return this.request<void>("/auth/revoke", {
      method: "POST",
    });
  }

  async getCurrentUser(): Promise<User> {
    return this.request<User>("/auth/me");
  }

  async getAllUsers(): Promise<User[]> {
    return this.request<User[]>("/user");
  }

  async updateUser(userId: string, userData: UpdateUserDto): Promise<User> {
    return this.request<User>(`/user/${userId}`, {
      method: "PUT",
      body: JSON.stringify(userData),
    });
  }

  async updateUserRole(
    userId: string,
    roleData: UpdateUserRoleDto
  ): Promise<void> {
    return this.request<void>(`/user/${userId}/role`, {
      method: "PUT",
      body: JSON.stringify(roleData),
    });
  }

  async deleteUser(userId: string): Promise<void> {
    return this.request<void>(`/user/${userId}`, {
      method: "DELETE",
    });
  }

  async toggleUserStatus(userId: string, isActive: boolean): Promise<User> {
    return this.request<User>(`/user/${userId}/status`, {
      method: "PUT",
      body: JSON.stringify({ isActive }),
    });
  }

  async createUser(userData: CreateUserDto): Promise<User> {
    return this.request<User>("/user", {
      method: "POST",
      body: JSON.stringify(userData),
    });
  }

  async getAllRoles(): Promise<Role[]> {
    return this.request<Role[]>("/user/roles");
  }

  async createRole(roleData: CreateRoleDto): Promise<Role> {
    return this.request<Role>("/user/roles", {
      method: "POST",
      body: JSON.stringify(roleData),
    });
  }

  async deleteRole(roleName: string): Promise<void> {
    return this.request<void>(`/user/roles/${encodeURIComponent(roleName)}`, {
      method: "DELETE",
    });
  }

  async changePassword(passwordData: {
    currentPassword: string;
    newPassword: string;
  }): Promise<void> {
    return this.request<void>("/user/change-password", {
      method: "POST",
      body: JSON.stringify(passwordData),
    });
  }

  async resetUserPassword(userId: string, newPassword: string): Promise<void> {
    return this.request<void>(`/user/${userId}/reset-password`, {
      method: "PUT",
      body: JSON.stringify({ newPassword }),
    });
  }

  // Production Methods
  async getArchivedProductionsPaginated(
    query: PaginationQuery
  ): Promise<PaginationData<Production>> {
    const params = new URLSearchParams();
    params.append("pageNumber", query.pageNumber.toString());
    params.append("pageSize", query.pageSize.toString());
    if (query.searchTerm) {
      params.append("searchTerm", query.searchTerm);
    }

    return this.request<PaginationData<Production>>(
      `/Production/archived/paginated?${params}`
    );
  }

  async getAllProductions(): Promise<Production[]> {
    return this.request<Production[]>("/Production");
  }

  async getProduction(id: number): Promise<Production> {
    return this.request<Production>(`/Production/${id}`);
  }

  async createProduction(productionData: any): Promise<Production> {
    return this.request<Production>("/Production/create-simple", {
      method: "POST",
      body: JSON.stringify(productionData),
    });
  }

  async updateProduction(id: number, productionData: any): Promise<Production> {
    return this.request<Production>(`/Production/${id}`, {
      method: "PUT",
      body: JSON.stringify(productionData),
    });
  }

  async deleteProduction(id: number): Promise<void> {
    return this.request<void>(`/Production/${id}`, {
      method: "DELETE",
    });
  }

  async archiveProduction(id: number): Promise<void> {
    return this.request<void>(`/Production/${id}/archive`, {
      method: "POST",
    });
  }

  async unarchiveProduction(id: number): Promise<void> {
    return this.request<void>(`/Production/${id}/unarchive`, {
      method: "POST",
    });
  }

  async assignProduction(
    productionId: number,
    userId: string
  ): Promise<Production> {
    return this.request<Production>(`/Production/${productionId}/assign`, {
      method: "POST",
      body: JSON.stringify(userId),
    });
  }

  async unassignProduction(productionId: number): Promise<Production> {
    return this.request<Production>(`/Production/${productionId}/unassign`, {
      method: "POST",
    });
  }

  async getProductionStats(): Promise<any> {
    return this.request<any>("/Production/stats");
  }

  async getDashboardStats(): Promise<any> {
    return this.request<any>("/Production/dashboard-stats");
  }

  async getTechnicianStats(userId: string): Promise<any> {
    return this.request<any>(`/Production/technician-stats/${userId}`);
  }

  async exportActiveExcel(): Promise<Blob> {
    const response = await fetch(
      `${this.baseURL}/Production/export-active-excel`,
      {
        method: "GET",
        headers: {
          Authorization: `Bearer ${useAuthStore.getState().token}`,
        },
      }
    );

    if (!response.ok) {
      throw new ApiError(response.status, await response.text());
    }

    return response.blob();
  }

  async exportArchivedExcel(): Promise<Blob> {
    const response = await fetch(
      `${this.baseURL}/Production/export-archived-excel`,
      {
        method: "GET",
        headers: {
          Authorization: `Bearer ${useAuthStore.getState().token}`,
        },
      }
    );

    if (!response.ok) {
      throw new ApiError(response.status, await response.text());
    }

    return response.blob();
  }

  async getNextEmailTime(): Promise<any> {
    return this.request<any>("/Production/next-email-time");
  }

  async sendDailyEmail(): Promise<void> {
    return this.request<void>("/Production/send-daily-email", {
      method: "POST",
    });
  }

  // User Methods
  async getTechnicians(): Promise<User[]> {
    return this.request<User[]>("/User/technicians");
  }

  async getSoftwareUsers(): Promise<User[]> {
    return this.request<User[]>("/User/software");
  }

  async getEditHistory(
    productionId: number,
    fieldName: string
  ): Promise<any[]> {
    return this.request<any[]>(
      `/Production/${productionId}/edit-history/${fieldName}`
    );
  }

  // Elegoo Printer Methods
  async getAllPrinters(): Promise<ElegooPrinter[]> {
    return this.request<ElegooPrinter[]>("/elegoo/printers");
  }

  async getPrinter(id: number): Promise<ElegooPrinter> {
    return this.request<ElegooPrinter>(`/elegoo/printers/${id}`);
  }

  async createPrinter(
    printerData: CreateElegooPrinterDto
  ): Promise<ElegooPrinter> {
    return this.request<ElegooPrinter>("/elegoo/printers", {
      method: "POST",
      body: JSON.stringify(printerData),
    });
  }

  async updatePrinter(
    id: number,
    printerData: UpdateElegooPrinterDto
  ): Promise<ElegooPrinter> {
    return this.request<ElegooPrinter>(`/elegoo/printers/${id}`, {
      method: "PUT",
      body: JSON.stringify(printerData),
    });
  }

  async deletePrinter(id: number): Promise<void> {
    return this.request<void>(`/elegoo/printers/${id}`, {
      method: "DELETE",
    });
  }

  // Printer Issues Methods
  async getPrinterIssues(printerId: number): Promise<PrinterIssue[]> {
    return this.request<PrinterIssue[]>(`/elegoo/printers/${printerId}/issues`);
  }

  async getIssue(id: number): Promise<PrinterIssue> {
    return this.request<PrinterIssue>(`/elegoo/issues/${id}`);
  }

  async createIssue(
    printerId: number,
    issueData: CreatePrinterIssueDto
  ): Promise<PrinterIssue> {
    return this.request<PrinterIssue>("/elegoo/issues", {
      method: "POST",
      body: JSON.stringify({
        ...issueData,
        printerId: printerId,
      }),
    });
  }

  async updateIssue(
    id: number,
    issueData: UpdatePrinterIssueDto
  ): Promise<PrinterIssue> {
    return this.request<PrinterIssue>(`/elegoo/issues/${id}`, {
      method: "PUT",
      body: JSON.stringify(issueData),
    });
  }

  async deleteIssue(id: number): Promise<void> {
    return this.request<void>(`/elegoo/issues/${id}`, {
      method: "DELETE",
    });
  }

  // Printer Maintenance Methods
  async getPrinterMaintenances(
    printerId: number
  ): Promise<PrinterMaintenance[]> {
    return this.request<PrinterMaintenance[]>(
      `/elegoo/printers/${printerId}/maintenances`
    );
  }

  async getMaintenance(id: number): Promise<PrinterMaintenance> {
    return this.request<PrinterMaintenance>(`/elegoo/maintenances/${id}`);
  }

  async createMaintenance(
    printerId: number,
    maintenanceData: CreatePrinterMaintenanceDto
  ): Promise<PrinterMaintenance> {
    return this.request<PrinterMaintenance>("/elegoo/maintenances", {
      method: "POST",
      body: JSON.stringify({
        ...maintenanceData,
        printerId: printerId,
      }),
    });
  }

  async updateMaintenance(
    id: number,
    maintenanceData: UpdatePrinterMaintenanceDto
  ): Promise<PrinterMaintenance> {
    return this.request<PrinterMaintenance>(`/elegoo/maintenances/${id}`, {
      method: "PUT",
      body: JSON.stringify(maintenanceData),
    });
  }

  async deleteMaintenance(id: number): Promise<void> {
    return this.request<void>(`/elegoo/maintenances/${id}`, {
      method: "DELETE",
    });
  }
}

export const apiService = new ApiService();
export { ApiError };
